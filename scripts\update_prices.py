"""
Automated price update script for continuous market monitoring.
This script can be scheduled to run periodically (e.g., every hour).
"""
import sys
import os
import asyncio
import logging
import schedule
import time
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import and_, or_
from app.database import SessionLocal
from app.services.market_service import MarketService
from app.models.modifier import Modifier
from app.models.market import MarketData
from app.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('price_updates.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PriceUpdater:
    """Automated price update system."""
    
    def __init__(self):
        self.db = SessionLocal()
        self.market_service = MarketService(self.db)
        self.update_interval = settings.data_update_interval_hours
    
    async def update_all_prices(self):
        """Update prices for all active modifiers."""
        logger.info("Starting automated price update...")
        
        try:
            start_time = datetime.utcnow()
            
            # Get modifiers that need updating
            modifiers_to_update = await self._get_modifiers_needing_update()
            
            if not modifiers_to_update:
                logger.info("No modifiers need updating at this time")
                return
            
            logger.info(f"Updating prices for {len(modifiers_to_update)} modifiers")
            
            # Update market data
            result = await self.market_service.refresh_market_data(
                [m.id for m in modifiers_to_update]
            )
            
            # Log results
            update_time = datetime.utcnow() - start_time
            logger.info(f"Price update completed in {update_time.total_seconds():.2f} seconds")
            logger.info(f"Updated {result.get('updated_count', 0)} modifiers")
            
            # Update statistics
            await self._update_statistics()
            
        except Exception as e:
            logger.error(f"Error during price update: {e}")
        finally:
            self.db.close()
    
    async def _get_modifiers_needing_update(self):
        """Get modifiers that need price updates."""
        cutoff_time = datetime.utcnow() - timedelta(hours=self.update_interval)
        
        # Get modifiers with outdated market data or no market data
        modifiers = self.db.query(Modifier).outerjoin(MarketData).filter(
            and_(
                Modifier.is_active == True,
                or_(
                    MarketData.last_updated.is_(None),
                    MarketData.last_updated < cutoff_time
                )
            )
        ).all()
        
        return modifiers
    
    async def _update_statistics(self):
        """Update market statistics after price updates."""
        try:
            # Calculate market-wide statistics
            market_data = self.db.query(MarketData).all()
            
            if market_data:
                # Calculate average price changes
                avg_24h_change = sum(md.price_change_24h for md in market_data) / len(market_data)
                avg_7d_change = sum(md.price_change_7d for md in market_data) / len(market_data)
                
                # Count trending items
                trending_up = sum(1 for md in market_data if md.price_change_24h > 5)
                trending_down = sum(1 for md in market_data if md.price_change_24h < -5)
                
                logger.info(f"Market statistics:")
                logger.info(f"  - Average 24h change: {avg_24h_change:.2f}%")
                logger.info(f"  - Average 7d change: {avg_7d_change:.2f}%")
                logger.info(f"  - Trending up: {trending_up}")
                logger.info(f"  - Trending down: {trending_down}")
                
        except Exception as e:
            logger.error(f"Error updating statistics: {e}")
    
    async def update_high_priority_items(self):
        """Update prices for high-priority/valuable items more frequently."""
        logger.info("Updating high-priority items...")
        
        try:
            # Get high-value modifiers
            high_value_modifiers = self.db.query(Modifier).filter(
                and_(
                    Modifier.is_active == True,
                    Modifier.is_valuable == True,
                    Modifier.base_value >= 0.5  # High value threshold
                )
            ).all()
            
            if high_value_modifiers:
                result = await self.market_service.refresh_market_data(
                    [m.id for m in high_value_modifiers]
                )
                logger.info(f"Updated {result.get('updated_count', 0)} high-priority items")
            
        except Exception as e:
            logger.error(f"Error updating high-priority items: {e}")
    
    async def detect_market_anomalies(self):
        """Detect and log market anomalies."""
        logger.info("Checking for market anomalies...")
        
        try:
            # Get recent price alerts
            alerts = await self.market_service.get_price_alerts(threshold_percent=20.0, hours=1)
            
            if alerts:
                logger.warning(f"Detected {len(alerts)} market anomalies:")
                for alert in alerts[:5]:  # Log first 5
                    logger.warning(f"  - {alert.modifier_name}: {alert.price_change_percent:.1f}% change")
            
        except Exception as e:
            logger.error(f"Error detecting market anomalies: {e}")


def setup_scheduler():
    """Setup the automated scheduling."""
    updater = PriceUpdater()
    
    # Schedule regular price updates
    schedule.every(settings.data_update_interval_hours).hours.do(
        lambda: asyncio.run(updater.update_all_prices())
    )
    
    # Schedule high-priority updates more frequently
    schedule.every(30).minutes.do(
        lambda: asyncio.run(updater.update_high_priority_items())
    )
    
    # Schedule anomaly detection
    schedule.every(15).minutes.do(
        lambda: asyncio.run(updater.detect_market_anomalies())
    )
    
    logger.info("Scheduler configured:")
    logger.info(f"  - Full updates every {settings.data_update_interval_hours} hours")
    logger.info("  - High-priority updates every 30 minutes")
    logger.info("  - Anomaly detection every 15 minutes")


async def run_once():
    """Run price update once and exit."""
    updater = PriceUpdater()
    await updater.update_all_prices()


def run_scheduler():
    """Run the continuous scheduler."""
    setup_scheduler()
    
    logger.info("Starting automated price update scheduler...")
    logger.info("Press Ctrl+C to stop")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    except KeyboardInterrupt:
        logger.info("Scheduler stopped by user")


async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="PoE 2 Tablet Optimizer Price Updater")
    parser.add_argument("--mode", choices=["once", "schedule", "priority", "anomalies"], 
                       default="once", help="Update mode")
    
    args = parser.parse_args()
    
    if args.mode == "once":
        await run_once()
    elif args.mode == "schedule":
        run_scheduler()
    elif args.mode == "priority":
        updater = PriceUpdater()
        await updater.update_high_priority_items()
    elif args.mode == "anomalies":
        updater = PriceUpdater()
        await updater.detect_market_anomalies()


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "schedule":
        run_scheduler()
    else:
        asyncio.run(main())
