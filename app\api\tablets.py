"""
API endpoints for tablet operations.
"""
from fastapi import APIRout<PERSON>, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.models.tablet import Tablet, TabletType
from app.schemas.tablet import TabletCreate, TabletResponse, TabletTypeResponse
from app.services.tablet_service import TabletService

router = APIRouter()


@router.get("/types", response_model=List[TabletTypeResponse])
async def get_tablet_types(
    active_only: bool = Query(True, description="Only return active tablet types"),
    db: Session = Depends(get_db)
):
    """Get all tablet types."""
    query = db.query(TabletType)
    if active_only:
        query = query.filter(TabletType.is_active == True)
    
    tablet_types = query.all()
    return tablet_types


@router.get("/types/{tablet_type_id}", response_model=TabletTypeResponse)
async def get_tablet_type(
    tablet_type_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific tablet type by ID."""
    tablet_type = db.query(TabletType).filter(TabletType.id == tablet_type_id).first()
    if not tablet_type:
        raise HTTPException(status_code=404, detail="Tablet type not found")
    return tablet_type


@router.post("/evaluate", response_model=TabletResponse)
async def evaluate_tablet(
    tablet_data: TabletCreate,
    db: Session = Depends(get_db)
):
    """
    Evaluate a tablet configuration and return value assessment.
    This is the core functionality for manual tablet evaluation.
    """
    tablet_service = TabletService(db)
    
    try:
        tablet = await tablet_service.evaluate_tablet(tablet_data)
        return tablet
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error during evaluation")


@router.get("/", response_model=List[TabletResponse])
async def get_tablets(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    min_value: Optional[float] = Query(None, description="Minimum total value"),
    max_value: Optional[float] = Query(None, description="Maximum total value"),
    is_profitable: Optional[bool] = Query(None, description="Filter by profitability"),
    limit: int = Query(50, le=100, description="Maximum number of results"),
    offset: int = Query(0, description="Number of results to skip"),
    db: Session = Depends(get_db)
):
    """Get tablets with optional filtering."""
    query = db.query(Tablet)
    
    if tablet_type_id:
        query = query.filter(Tablet.tablet_type_id == tablet_type_id)
    
    if min_value is not None:
        query = query.filter(Tablet.total_value >= min_value)
    
    if max_value is not None:
        query = query.filter(Tablet.total_value <= max_value)
    
    if is_profitable is not None:
        if is_profitable:
            query = query.filter(Tablet.profit_estimate > 0)
        else:
            query = query.filter(Tablet.profit_estimate <= 0)
    
    tablets = query.offset(offset).limit(limit).all()
    return tablets


@router.get("/{tablet_id}", response_model=TabletResponse)
async def get_tablet(
    tablet_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific tablet by ID."""
    tablet = db.query(Tablet).filter(Tablet.id == tablet_id).first()
    if not tablet:
        raise HTTPException(status_code=404, detail="Tablet not found")
    return tablet


@router.put("/{tablet_id}/sold")
async def mark_tablet_sold(
    tablet_id: int,
    sale_price: float,
    db: Session = Depends(get_db)
):
    """Mark a tablet as sold with the actual sale price."""
    tablet = db.query(Tablet).filter(Tablet.id == tablet_id).first()
    if not tablet:
        raise HTTPException(status_code=404, detail="Tablet not found")
    
    tablet_service = TabletService(db)
    await tablet_service.mark_sold(tablet, sale_price)
    
    return {"message": "Tablet marked as sold", "actual_profit": sale_price - (tablet.tablet_type.base_cost + tablet.tablet_type.crafting_cost)}


@router.get("/stats/summary")
async def get_tablet_stats(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    days: int = Query(30, description="Number of days to analyze"),
    db: Session = Depends(get_db)
):
    """Get summary statistics for tablets."""
    tablet_service = TabletService(db)
    stats = await tablet_service.get_summary_stats(tablet_type_id, days)
    return stats
