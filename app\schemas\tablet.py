"""
Pydantic schemas for tablet-related API operations.
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class TabletTypeBase(BaseModel):
    """Base schema for tablet types."""
    name: str = Field(..., description="Internal name of the tablet type")
    display_name: str = Field(..., description="Display name for users")
    description: Optional[str] = Field(None, description="Description of the tablet type")
    base_cost: float = Field(0.0, description="Base cost in exalted orbs")
    crafting_cost: float = Field(0.0, description="Average crafting cost")


class TabletTypeCreate(TabletTypeBase):
    """Schema for creating a new tablet type."""
    pass


class TabletTypeResponse(TabletTypeBase):
    """Schema for tablet type API responses."""
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class TabletBase(BaseModel):
    """Base schema for tablets."""
    tablet_type_id: int = Field(..., description="ID of the tablet type")
    modifier1_id: Optional[int] = Field(None, description="First modifier ID")
    modifier1_tier: int = Field(1, description="Tier of first modifier")
    modifier2_id: Optional[int] = Field(None, description="Second modifier ID")
    modifier2_tier: int = Field(1, description="Tier of second modifier")
    user_notes: Optional[str] = Field(None, description="User notes about the tablet")


class TabletCreate(TabletBase):
    """Schema for creating/evaluating a new tablet."""
    pass


class TabletUpdate(BaseModel):
    """Schema for updating tablet information."""
    user_notes: Optional[str] = None
    is_sold: Optional[bool] = None
    actual_sale_price: Optional[float] = None


class TabletResponse(TabletBase):
    """Schema for tablet API responses."""
    id: int
    base_value: float = Field(..., description="Sum of individual modifier values")
    synergy_bonus: float = Field(..., description="Bonus for modifier combinations")
    total_value: float = Field(..., description="Total market value")
    profit_estimate: float = Field(..., description="Estimated profit after costs")
    market_confidence: float = Field(..., description="Confidence in valuation (0-1)")
    last_market_check: Optional[datetime]
    is_sold: bool
    actual_sale_price: Optional[float]
    sale_date: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    
    # Computed properties
    is_profitable: bool = Field(..., description="Whether tablet is profitable to sell")
    value_tier: str = Field(..., description="Value tier: high, medium, or low")
    
    # Related data
    tablet_type: Optional[TabletTypeResponse] = None
    
    class Config:
        from_attributes = True


class TabletSummaryStats(BaseModel):
    """Schema for tablet summary statistics."""
    total_tablets: int
    profitable_tablets: int
    average_value: float
    average_profit: float
    total_profit: float
    success_rate: float = Field(..., description="Percentage of profitable tablets")
    
    # Value tier breakdown
    high_value_count: int
    medium_value_count: int
    low_value_count: int
    
    # Recent performance
    tablets_last_24h: int
    profit_last_24h: float
    
    # Top performers
    best_tablet_value: float
    worst_tablet_value: float
    most_common_modifier1: Optional[str]
    most_common_modifier2: Optional[str]


class TabletEvaluationResult(BaseModel):
    """Schema for tablet evaluation results."""
    tablet: TabletResponse
    evaluation_details: dict = Field(..., description="Detailed evaluation breakdown")
    recommendations: List[str] = Field(..., description="Recommendations based on evaluation")
    market_context: dict = Field(..., description="Current market context")
    risk_assessment: dict = Field(..., description="Risk factors and assessment")
