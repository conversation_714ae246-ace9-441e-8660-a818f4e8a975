{% extends "base.html" %}

{% block title %}Dashboard - PoE 2 Tablet Optimizer{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">PoE 2 Tablet Crafting Profit Optimizer</h1>
        <p class="lead">Maximize your tablet crafting profits with real-time market intelligence and predictive analytics.</p>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5 class="card-title">Active Markets</h5>
                <h2 class="card-text" id="active-markets">Loading...</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5 class="card-title">Trending Up</h5>
                <h2 class="card-text" id="trending-up">Loading...</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <h5 class="card-title">High Volatility</h5>
                <h2 class="card-text" id="high-volatility">Loading...</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h5 class="card-title">Avg Price Change</h5>
                <h2 class="card-text" id="avg-price-change">Loading...</h2>
            </div>
        </div>
    </div>
</div>

<!-- Main Features -->
<div class="row">
    <!-- Regex Generator -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">🔍 Regex Pattern Generator</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Generate regex patterns for valuable modifiers to use in PoE 2 inventory search.</p>
                
                <div class="mb-3">
                    <label for="tablet-type-select" class="form-label">Tablet Type:</label>
                    <select class="form-select" id="tablet-type-select">
                        <option value="">Loading tablet types...</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="value-tier-select" class="form-label">Value Tier:</label>
                    <select class="form-select" id="value-tier-select">
                        <option value="high">High Value (0.8+ ex)</option>
                        <option value="medium">Medium Value (0.4-0.8 ex)</option>
                        <option value="low">Low Value (<0.4 ex)</option>
                        <option value="all">All Tiers</option>
                    </select>
                </div>
                
                <button class="btn btn-primary" id="generate-patterns">Generate Patterns</button>
                <a href="/regex" class="btn btn-outline-primary">Advanced Options</a>
                
                <div id="pattern-results" class="mt-3" style="display: none;">
                    <h6>Generated Patterns:</h6>
                    <div id="pattern-list"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Manual Calculator -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">🧮 Manual Tablet Calculator</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Evaluate specific tablet configurations for detailed profit analysis.</p>
                
                <div class="mb-3">
                    <label for="calc-tablet-type" class="form-label">Tablet Type:</label>
                    <select class="form-select" id="calc-tablet-type">
                        <option value="">Select tablet type...</option>
                    </select>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="modifier1" class="form-label">Modifier 1:</label>
                        <select class="form-select" id="modifier1">
                            <option value="">Select modifier...</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="modifier2" class="form-label">Modifier 2:</label>
                        <select class="form-select" id="modifier2">
                            <option value="">Select modifier...</option>
                        </select>
                    </div>
                </div>
                
                <button class="btn btn-success" id="calculate-value">Calculate Value</button>
                <a href="/calculator" class="btn btn-outline-success">Advanced Calculator</a>
                
                <div id="calculation-results" class="mt-3" style="display: none;">
                    <div class="alert alert-info">
                        <strong>Estimated Value:</strong> <span id="estimated-value">0</span> ex<br>
                        <strong>Profit Estimate:</strong> <span id="profit-estimate">0</span> ex<br>
                        <strong>Recommendation:</strong> <span id="recommendation">-</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Market Overview -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">📈 Market Overview</h5>
            </div>
            <div class="card-body">
                <canvas id="market-overview-chart" width="400" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">🔥 Trending Modifiers</h5>
            </div>
            <div class="card-body">
                <div id="trending-modifiers">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">⚠️ Price Alerts</h5>
            </div>
            <div class="card-body">
                <div id="price-alerts">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    loadTabletTypes();
    setupEventListeners();
    
    // Refresh data every 5 minutes
    setInterval(loadDashboardData, 300000);
});

function loadDashboardData() {
    // Load market overview
    fetch('/api/market/overview/dashboard')
        .then(response => response.json())
        .then(data => {
            document.getElementById('active-markets').textContent = data.active_markets || 0;
            document.getElementById('trending-up').textContent = data.trending_up_count || 0;
            document.getElementById('high-volatility').textContent = data.high_volatility_count || 0;
            document.getElementById('avg-price-change').textContent = 
                (data.average_price_change_24h || 0).toFixed(1) + '%';
            
            updateLastUpdateTime();
        })
        .catch(error => console.error('Error loading dashboard data:', error));
    
    // Load trending modifiers
    loadTrendingModifiers();
    
    // Load price alerts
    loadPriceAlerts();
}

function loadTabletTypes() {
    fetch('/api/tablets/types')
        .then(response => response.json())
        .then(data => {
            const selects = ['tablet-type-select', 'calc-tablet-type'];
            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                select.innerHTML = '<option value="">Select tablet type...</option>';
                data.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.display_name;
                    select.appendChild(option);
                });
            });
        })
        .catch(error => console.error('Error loading tablet types:', error));
}

function setupEventListeners() {
    document.getElementById('generate-patterns').addEventListener('click', generatePatterns);
    document.getElementById('calculate-value').addEventListener('click', calculateValue);
    document.getElementById('calc-tablet-type').addEventListener('change', loadModifiersForTablet);
}

function generatePatterns() {
    const tabletTypeId = document.getElementById('tablet-type-select').value;
    const valueTier = document.getElementById('value-tier-select').value;
    
    if (!tabletTypeId) {
        alert('Please select a tablet type');
        return;
    }
    
    const button = document.getElementById('generate-patterns');
    button.disabled = true;
    button.textContent = 'Generating...';
    
    fetch(`/api/regex/patterns/valuable?tablet_type_id=${tabletTypeId}&value_tier=${valueTier}`)
        .then(response => response.json())
        .then(data => {
            displayPatterns(data);
        })
        .catch(error => {
            console.error('Error generating patterns:', error);
            alert('Error generating patterns. Please try again.');
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = 'Generate Patterns';
        });
}

function displayPatterns(patterns) {
    const resultsDiv = document.getElementById('pattern-results');
    const listDiv = document.getElementById('pattern-list');
    
    if (patterns.length === 0) {
        listDiv.innerHTML = '<div class="alert alert-warning">No patterns generated. Try a different value tier.</div>';
    } else {
        listDiv.innerHTML = patterns.map(pattern => `
            <div class="mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <code class="pattern-code">${pattern.pattern}</code>
                    <button class="btn btn-sm btn-outline-primary copy-pattern" data-pattern="${pattern.pattern}">Copy</button>
                </div>
                <small class="text-muted">${pattern.description}</small>
            </div>
        `).join('');
        
        // Add copy functionality
        document.querySelectorAll('.copy-pattern').forEach(button => {
            button.addEventListener('click', function() {
                navigator.clipboard.writeText(this.dataset.pattern);
                this.textContent = 'Copied!';
                setTimeout(() => this.textContent = 'Copy', 2000);
            });
        });
    }
    
    resultsDiv.style.display = 'block';
}

function loadTrendingModifiers() {
    fetch('/api/modifiers/trending/up?limit=5')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('trending-modifiers');
            if (data.length === 0) {
                container.innerHTML = '<p class="text-muted">No trending modifiers found.</p>';
            } else {
                container.innerHTML = data.map(item => `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>${item.modifier.display_name}</strong><br>
                            <small class="text-muted">${item.modifier.tablet_type?.display_name || 'Unknown'}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-success">+${item.price_change_percent.toFixed(1)}%</span><br>
                            <small>${item.modifier.current_value.toFixed(2)} ex</small>
                        </div>
                    </div>
                `).join('');
            }
        })
        .catch(error => {
            console.error('Error loading trending modifiers:', error);
            document.getElementById('trending-modifiers').innerHTML = 
                '<p class="text-danger">Error loading trending modifiers.</p>';
        });
}

function loadPriceAlerts() {
    fetch('/api/market/alerts/price-changes?threshold_percent=15&hours=24')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('price-alerts');
            if (data.length === 0) {
                container.innerHTML = '<p class="text-muted">No significant price changes in the last 24 hours.</p>';
            } else {
                container.innerHTML = data.slice(0, 5).map(alert => `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>${alert.modifier_name}</strong><br>
                            <small class="text-muted">${alert.old_price.toFixed(2)} → ${alert.new_price.toFixed(2)} ex</small>
                        </div>
                        <div class="text-end">
                            <span class="badge ${alert.price_change_percent > 0 ? 'bg-success' : 'bg-danger'}">
                                ${alert.price_change_percent > 0 ? '+' : ''}${alert.price_change_percent.toFixed(1)}%
                            </span><br>
                            <small>${alert.severity}</small>
                        </div>
                    </div>
                `).join('');
            }
        })
        .catch(error => {
            console.error('Error loading price alerts:', error);
            document.getElementById('price-alerts').innerHTML = 
                '<p class="text-danger">Error loading price alerts.</p>';
        });
}

function updateLastUpdateTime() {
    document.getElementById('update-time').textContent = new Date().toLocaleTimeString();
}

// Placeholder functions for calculator
function loadModifiersForTablet() {
    // TODO: Implement modifier loading based on selected tablet type
}

function calculateValue() {
    // TODO: Implement tablet value calculation
    alert('Calculator functionality will be implemented in the next phase.');
}
</script>
{% endblock %}
