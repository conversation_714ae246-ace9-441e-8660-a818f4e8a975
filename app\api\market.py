"""
API endpoints for market data and analysis.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
from app.database import get_db
from app.models.market import MarketData, PriceHistory, MarketTrend
from app.schemas.market import MarketDataResponse, PriceHistoryResponse, MarketTrendResponse
from app.services.market_service import MarketService

router = APIRouter()


@router.get("/data", response_model=List[MarketDataResponse])
async def get_market_data(
    modifier_ids: Optional[str] = Query(None, description="Comma-separated modifier IDs"),
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    min_price: Optional[float] = Query(None, description="Minimum price filter"),
    max_price: Optional[float] = Query(None, description="Maximum price filter"),
    trending_only: bool = Query(False, description="Only return trending items"),
    limit: int = Query(50, le=200, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """Get current market data with optional filtering."""
    market_service = MarketService(db)
    
    modifier_id_list = None
    if modifier_ids:
        try:
            modifier_id_list = [int(x.strip()) for x in modifier_ids.split(",")]
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid modifier IDs format")
    
    market_data = await market_service.get_market_data(
        modifier_ids=modifier_id_list,
        tablet_type_id=tablet_type_id,
        min_price=min_price,
        max_price=max_price,
        trending_only=trending_only,
        limit=limit
    )
    
    return market_data


@router.get("/data/{modifier_id}", response_model=MarketDataResponse)
async def get_modifier_market_data(
    modifier_id: int,
    db: Session = Depends(get_db)
):
    """Get market data for a specific modifier."""
    market_data = db.query(MarketData).filter(MarketData.modifier_id == modifier_id).first()
    if not market_data:
        raise HTTPException(status_code=404, detail="Market data not found for this modifier")
    return market_data


@router.get("/history/{modifier_id}", response_model=List[PriceHistoryResponse])
async def get_price_history(
    modifier_id: int,
    days: int = Query(30, le=365, description="Number of days of history"),
    interval: str = Query("daily", regex="^(hourly|daily|weekly)$", description="Data interval"),
    db: Session = Depends(get_db)
):
    """Get price history for a modifier."""
    market_service = MarketService(db)
    history = await market_service.get_price_history(modifier_id, days, interval)
    return history


@router.get("/trends", response_model=List[MarketTrendResponse])
async def get_market_trends(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    trend_type: Optional[str] = Query(None, regex="^(bullish|bearish|sideways)$", description="Filter by trend type"),
    min_confidence: float = Query(0.5, description="Minimum prediction confidence"),
    limit: int = Query(50, le=100, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """Get market trends and predictions."""
    market_service = MarketService(db)
    trends = await market_service.get_market_trends(
        tablet_type_id=tablet_type_id,
        trend_type=trend_type,
        min_confidence=min_confidence,
        limit=limit
    )
    return trends


@router.get("/trends/{modifier_id}", response_model=MarketTrendResponse)
async def get_modifier_trend(
    modifier_id: int,
    db: Session = Depends(get_db)
):
    """Get trend analysis for a specific modifier."""
    trend = db.query(MarketTrend).filter(MarketTrend.modifier_id == modifier_id).first()
    if not trend:
        raise HTTPException(status_code=404, detail="Trend data not found for this modifier")
    return trend


@router.get("/overview/dashboard")
async def get_market_overview(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    db: Session = Depends(get_db)
):
    """Get market overview data for dashboard."""
    market_service = MarketService(db)
    overview = await market_service.get_market_overview(tablet_type_id)
    return overview


@router.get("/alerts/price-changes")
async def get_price_alerts(
    threshold_percent: float = Query(10.0, description="Minimum price change percentage"),
    hours: int = Query(24, description="Time period to check"),
    db: Session = Depends(get_db)
):
    """Get significant price changes in the specified time period."""
    market_service = MarketService(db)
    alerts = await market_service.get_price_alerts(threshold_percent, hours)
    return alerts


@router.get("/volatility/high")
async def get_high_volatility_modifiers(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    min_volatility: float = Query(0.3, description="Minimum volatility score"),
    limit: int = Query(20, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """Get modifiers with high price volatility."""
    market_service = MarketService(db)
    volatile = await market_service.get_high_volatility_modifiers(
        tablet_type_id=tablet_type_id,
        min_volatility=min_volatility,
        limit=limit
    )
    return volatile


@router.get("/opportunities/arbitrage")
async def get_arbitrage_opportunities(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    min_profit_margin: float = Query(0.1, description="Minimum profit margin"),
    limit: int = Query(10, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """Get potential arbitrage opportunities."""
    market_service = MarketService(db)
    opportunities = await market_service.get_arbitrage_opportunities(
        tablet_type_id=tablet_type_id,
        min_profit_margin=min_profit_margin,
        limit=limit
    )
    return opportunities


@router.post("/data/refresh")
async def refresh_market_data(
    modifier_ids: Optional[str] = Query(None, description="Comma-separated modifier IDs to refresh"),
    db: Session = Depends(get_db)
):
    """Trigger manual refresh of market data."""
    market_service = MarketService(db)
    
    modifier_id_list = None
    if modifier_ids:
        try:
            modifier_id_list = [int(x.strip()) for x in modifier_ids.split(",")]
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid modifier IDs format")
    
    result = await market_service.refresh_market_data(modifier_id_list)
    return {
        "message": "Market data refresh initiated",
        "modifiers_updated": result.get("updated_count", 0),
        "timestamp": datetime.utcnow()
    }
