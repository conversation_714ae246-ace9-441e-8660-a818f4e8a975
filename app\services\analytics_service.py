"""
Predictive analytics service for market trend analysis and price predictions.
Uses machine learning models to forecast market movements and identify opportunities.
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
import logging
import pickle
import os

from app.models.market import MarketData, PriceHistory, MarketTrend
from app.models.modifier import Modifier
from app.models.analytics import PredictionModel, TrendAnalysis
from app.schemas.analytics import PredictionResponse, TrendAnalysisResponse, MarketForecast, OpportunityAlert
from app.config import settings

logger = logging.getLogger(__name__)


class AnalyticsService:
    """Advanced predictive analytics for market intelligence."""
    
    def __init__(self, db: Session):
        self.db = db
        self.models = {}
        self.scalers = {}
        self.model_cache_dir = "models"
        self._ensure_model_directory()
    
    def _ensure_model_directory(self):
        """Ensure model cache directory exists."""
        if not os.path.exists(self.model_cache_dir):
            os.makedirs(self.model_cache_dir)
    
    async def predict_price_movement(self, modifier_id: int, days_ahead: int = 7, 
                                   confidence_threshold: float = 0.7) -> PredictionResponse:
        """Predict price movement for a specific modifier."""
        try:
            # Get historical data
            historical_data = await self._get_historical_features(modifier_id)
            
            if len(historical_data) < 10:
                return PredictionResponse(
                    modifier_id=modifier_id,
                    prediction_type="price_movement",
                    predicted_value=0.0,
                    confidence=0.0,
                    prediction_horizon_days=days_ahead,
                    error_message="Insufficient historical data"
                )
            
            # Prepare features
            features = self._prepare_features(historical_data)
            
            # Get or train model
            model = await self._get_or_train_model(modifier_id, "price_movement")
            
            if not model:
                return PredictionResponse(
                    modifier_id=modifier_id,
                    prediction_type="price_movement",
                    predicted_value=0.0,
                    confidence=0.0,
                    prediction_horizon_days=days_ahead,
                    error_message="Model training failed"
                )
            
            # Make prediction
            latest_features = features.iloc[-1:].values
            predicted_change = model.predict(latest_features)[0]
            
            # Calculate confidence based on model performance
            confidence = self._calculate_prediction_confidence(model, features)
            
            # Get current price for context
            current_market_data = self.db.query(MarketData).filter(
                MarketData.modifier_id == modifier_id
            ).first()
            
            current_price = current_market_data.current_price if current_market_data else 0.0
            predicted_price = current_price * (1 + predicted_change / 100)
            
            return PredictionResponse(
                modifier_id=modifier_id,
                prediction_type="price_movement",
                predicted_value=predicted_price,
                predicted_change_percent=predicted_change,
                confidence=confidence,
                prediction_horizon_days=days_ahead,
                current_value=current_price,
                factors_analyzed=self._get_prediction_factors(historical_data),
                model_accuracy=getattr(model, 'score_', 0.0)
            )
            
        except Exception as e:
            logger.error(f"Error predicting price movement for modifier {modifier_id}: {e}")
            return PredictionResponse(
                modifier_id=modifier_id,
                prediction_type="price_movement",
                predicted_value=0.0,
                confidence=0.0,
                prediction_horizon_days=days_ahead,
                error_message=str(e)
            )
    
    async def analyze_market_trends(self, tablet_type_id: Optional[int] = None, 
                                  days_back: int = 30) -> List[TrendAnalysisResponse]:
        """Analyze market trends for modifiers."""
        try:
            # Get modifiers to analyze
            query = self.db.query(Modifier).filter(Modifier.is_active == True)
            if tablet_type_id:
                query = query.filter(Modifier.tablet_type_id == tablet_type_id)
            
            modifiers = query.all()
            trend_analyses = []
            
            for modifier in modifiers:
                trend_analysis = await self._analyze_modifier_trend(modifier, days_back)
                if trend_analysis:
                    trend_analyses.append(trend_analysis)
            
            # Sort by trend strength
            trend_analyses.sort(key=lambda x: x.trend_strength, reverse=True)
            
            return trend_analyses
            
        except Exception as e:
            logger.error(f"Error analyzing market trends: {e}")
            return []
    
    async def _analyze_modifier_trend(self, modifier: Modifier, days_back: int) -> Optional[TrendAnalysisResponse]:
        """Analyze trend for a specific modifier."""
        try:
            # Get price history
            since_date = datetime.utcnow() - timedelta(days=days_back)
            
            price_history = self.db.query(PriceHistory).join(MarketData).filter(
                and_(
                    MarketData.modifier_id == modifier.id,
                    PriceHistory.timestamp >= since_date
                )
            ).order_by(PriceHistory.timestamp).all()
            
            if len(price_history) < 5:
                return None
            
            # Calculate trend metrics
            prices = [ph.price for ph in price_history]
            timestamps = [ph.timestamp for ph in price_history]
            
            # Linear regression for trend
            X = np.array([(ts - timestamps[0]).total_seconds() / 86400 for ts in timestamps]).reshape(-1, 1)
            y = np.array(prices)
            
            lr = LinearRegression()
            lr.fit(X, y)
            
            trend_slope = lr.coef_[0]
            trend_strength = abs(lr.score(X, y))  # R-squared as strength measure
            
            # Determine trend direction
            if trend_slope > 0.01:
                trend_direction = "upward"
            elif trend_slope < -0.01:
                trend_direction = "downward"
            else:
                trend_direction = "sideways"
            
            # Calculate volatility
            price_changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
            volatility = np.std(price_changes) / np.mean(prices) if prices else 0
            
            # Calculate momentum
            recent_prices = prices[-7:] if len(prices) >= 7 else prices
            momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0] if recent_prices else 0
            
            return TrendAnalysisResponse(
                modifier_id=modifier.id,
                modifier_name=modifier.display_name,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                trend_slope=trend_slope,
                volatility=volatility,
                momentum=momentum,
                analysis_period_days=days_back,
                data_points_analyzed=len(price_history),
                confidence=min(trend_strength * (len(price_history) / 30), 1.0),
                key_insights=self._generate_trend_insights(trend_direction, trend_strength, volatility, momentum)
            )
            
        except Exception as e:
            logger.error(f"Error analyzing trend for modifier {modifier.id}: {e}")
            return None
    
    def _generate_trend_insights(self, direction: str, strength: float, volatility: float, momentum: float) -> List[str]:
        """Generate human-readable insights from trend analysis."""
        insights = []
        
        if strength > 0.7:
            insights.append(f"Strong {direction} trend detected")
        elif strength > 0.4:
            insights.append(f"Moderate {direction} trend")
        else:
            insights.append("Weak or no clear trend")
        
        if volatility > 0.3:
            insights.append("High price volatility - risky investment")
        elif volatility < 0.1:
            insights.append("Low volatility - stable price movement")
        
        if momentum > 0.1:
            insights.append("Strong positive momentum")
        elif momentum < -0.1:
            insights.append("Strong negative momentum")
        
        return insights
    
    async def generate_market_forecast(self, days_ahead: int = 14) -> MarketForecast:
        """Generate comprehensive market forecast."""
        try:
            # Get all active modifiers
            modifiers = self.db.query(Modifier).filter(Modifier.is_active == True).all()
            
            predictions = []
            trend_predictions = []
            
            for modifier in modifiers[:20]:  # Limit to top 20 for performance
                # Get price prediction
                price_pred = await self.predict_price_movement(modifier.id, days_ahead)
                if price_pred.confidence > 0.5:
                    predictions.append(price_pred)
                
                # Get trend analysis
                trend_analysis = await self._analyze_modifier_trend(modifier, 30)
                if trend_analysis and trend_analysis.confidence > 0.5:
                    trend_predictions.append(trend_analysis)
            
            # Calculate market-wide metrics
            overall_sentiment = self._calculate_market_sentiment(predictions)
            volatility_index = self._calculate_volatility_index(trend_predictions)
            opportunity_score = self._calculate_opportunity_score(predictions)
            
            return MarketForecast(
                forecast_date=datetime.utcnow(),
                forecast_horizon_days=days_ahead,
                overall_market_sentiment=overall_sentiment,
                volatility_index=volatility_index,
                opportunity_score=opportunity_score,
                predicted_price_movements=predictions,
                trend_analyses=trend_predictions,
                key_opportunities=self._identify_key_opportunities(predictions),
                risk_warnings=self._identify_risk_warnings(predictions, trend_predictions),
                confidence=self._calculate_forecast_confidence(predictions)
            )
            
        except Exception as e:
            logger.error(f"Error generating market forecast: {e}")
            return MarketForecast(
                forecast_date=datetime.utcnow(),
                forecast_horizon_days=days_ahead,
                overall_market_sentiment="unknown",
                volatility_index=0.5,
                opportunity_score=0.0,
                predicted_price_movements=[],
                trend_analyses=[],
                key_opportunities=[],
                risk_warnings=["Forecast generation failed"],
                confidence=0.0
            )
    
    def _calculate_market_sentiment(self, predictions: List[PredictionResponse]) -> str:
        """Calculate overall market sentiment from predictions."""
        if not predictions:
            return "neutral"
        
        positive_predictions = sum(1 for p in predictions if p.predicted_change_percent > 5)
        negative_predictions = sum(1 for p in predictions if p.predicted_change_percent < -5)
        
        total_predictions = len(predictions)
        positive_ratio = positive_predictions / total_predictions
        negative_ratio = negative_predictions / total_predictions
        
        if positive_ratio > 0.6:
            return "bullish"
        elif negative_ratio > 0.6:
            return "bearish"
        elif positive_ratio > negative_ratio:
            return "cautiously_optimistic"
        elif negative_ratio > positive_ratio:
            return "cautiously_pessimistic"
        else:
            return "neutral"
    
    def _calculate_volatility_index(self, trend_analyses: List[TrendAnalysisResponse]) -> float:
        """Calculate market volatility index."""
        if not trend_analyses:
            return 0.5
        
        volatilities = [ta.volatility for ta in trend_analyses]
        return sum(volatilities) / len(volatilities)
    
    def _calculate_opportunity_score(self, predictions: List[PredictionResponse]) -> float:
        """Calculate overall market opportunity score."""
        if not predictions:
            return 0.0
        
        # Score based on predicted returns and confidence
        opportunity_scores = []
        for pred in predictions:
            if pred.confidence > 0.6 and pred.predicted_change_percent > 10:
                opportunity_scores.append(pred.confidence * (pred.predicted_change_percent / 100))
        
        return sum(opportunity_scores) / len(predictions) if opportunity_scores else 0.0
    
    async def _get_historical_features(self, modifier_id: int, days_back: int = 90) -> pd.DataFrame:
        """Get historical features for model training."""
        since_date = datetime.utcnow() - timedelta(days=days_back)
        
        # Get price history
        price_history = self.db.query(PriceHistory).join(MarketData).filter(
            and_(
                MarketData.modifier_id == modifier_id,
                PriceHistory.timestamp >= since_date
            )
        ).order_by(PriceHistory.timestamp).all()
        
        if not price_history:
            return pd.DataFrame()
        
        # Convert to DataFrame
        data = []
        for ph in price_history:
            data.append({
                'timestamp': ph.timestamp,
                'price': ph.price,
                'volume': ph.volume,
                'listings_count': ph.listings_count
            })
        
        df = pd.DataFrame(data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp')
        
        return df
    
    def _prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for machine learning models."""
        if df.empty:
            return df
        
        # Calculate technical indicators
        df['price_change'] = df['price'].pct_change()
        df['volume_change'] = df['volume'].pct_change()
        df['price_ma_7'] = df['price'].rolling(window=7).mean()
        df['price_ma_14'] = df['price'].rolling(window=14).mean()
        df['volume_ma_7'] = df['volume'].rolling(window=7).mean()
        
        # Calculate relative strength index (simplified)
        df['rsi'] = self._calculate_rsi(df['price'])
        
        # Calculate volatility
        df['volatility'] = df['price_change'].rolling(window=7).std()
        
        # Day of week and hour features
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['hour'] = df['timestamp'].dt.hour
        
        # Drop NaN values
        df = df.dropna()
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Relative Strength Index."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    async def _get_or_train_model(self, modifier_id: int, model_type: str):
        """Get existing model or train a new one."""
        model_key = f"{modifier_id}_{model_type}"
        
        # Check if model exists in cache
        if model_key in self.models:
            return self.models[model_key]
        
        # Try to load from disk
        model_path = os.path.join(self.model_cache_dir, f"{model_key}.pkl")
        if os.path.exists(model_path):
            try:
                with open(model_path, 'rb') as f:
                    model = pickle.load(f)
                    self.models[model_key] = model
                    return model
            except Exception as e:
                logger.warning(f"Failed to load model from {model_path}: {e}")
        
        # Train new model
        return await self._train_model(modifier_id, model_type)
    
    async def _train_model(self, modifier_id: int, model_type: str):
        """Train a new prediction model."""
        try:
            # Get training data
            historical_data = await self._get_historical_features(modifier_id, days_back=180)
            
            if len(historical_data) < 30:
                logger.warning(f"Insufficient data for training model for modifier {modifier_id}")
                return None
            
            # Prepare features and target
            features = self._prepare_features(historical_data)
            
            if features.empty:
                return None
            
            # Define feature columns
            feature_columns = ['price_change', 'volume_change', 'price_ma_7', 'price_ma_14', 
                             'volume_ma_7', 'rsi', 'volatility', 'day_of_week', 'hour']
            
            # Filter available columns
            available_columns = [col for col in feature_columns if col in features.columns]
            
            if len(available_columns) < 3:
                logger.warning(f"Insufficient features for training model for modifier {modifier_id}")
                return None
            
            X = features[available_columns].values
            
            # Create target (next day price change)
            y = features['price_change'].shift(-1).dropna().values
            X = X[:-1]  # Remove last row to match target length
            
            if len(X) < 10:
                return None
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train model
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            y_pred = model.predict(X_test_scaled)
            score = r2_score(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            
            logger.info(f"Trained model for modifier {modifier_id}: R² = {score:.3f}, MAE = {mae:.3f}")
            
            # Store model and scaler
            model_key = f"{modifier_id}_{model_type}"
            self.models[model_key] = model
            self.scalers[model_key] = scaler
            
            # Save to disk
            model_path = os.path.join(self.model_cache_dir, f"{model_key}.pkl")
            with open(model_path, 'wb') as f:
                pickle.dump({'model': model, 'scaler': scaler, 'features': available_columns}, f)
            
            return model
            
        except Exception as e:
            logger.error(f"Error training model for modifier {modifier_id}: {e}")
            return None

    def _calculate_prediction_confidence(self, model, features: pd.DataFrame) -> float:
        """Calculate confidence score for predictions."""
        try:
            # Use model score as base confidence
            if hasattr(model, 'score_'):
                base_confidence = max(0.0, min(1.0, model.score_))
            else:
                base_confidence = 0.5

            # Adjust based on data quality
            data_quality_factor = min(len(features) / 50, 1.0)  # More data = higher confidence

            return base_confidence * data_quality_factor
        except:
            return 0.5

    def _get_prediction_factors(self, historical_data: pd.DataFrame) -> List[str]:
        """Get factors that influenced the prediction."""
        factors = ["Price history", "Volume trends"]

        if len(historical_data) > 30:
            factors.append("Long-term trends")

        if 'volatility' in historical_data.columns:
            factors.append("Market volatility")

        return factors

    def _identify_key_opportunities(self, predictions: List[PredictionResponse]) -> List[str]:
        """Identify key market opportunities."""
        opportunities = []

        # High confidence positive predictions
        strong_buys = [p for p in predictions if p.confidence > 0.7 and p.predicted_change_percent > 15]
        if strong_buys:
            opportunities.append(f"{len(strong_buys)} modifiers with strong buy signals")

        # Undervalued items
        undervalued = [p for p in predictions if p.predicted_change_percent > 20 and p.confidence > 0.6]
        if undervalued:
            opportunities.append(f"{len(undervalued)} potentially undervalued items")

        return opportunities

    def _identify_risk_warnings(self, predictions: List[PredictionResponse],
                              trends: List[TrendAnalysisResponse]) -> List[str]:
        """Identify market risk warnings."""
        warnings = []

        # High volatility warnings
        high_vol = [t for t in trends if t.volatility > 0.4]
        if len(high_vol) > len(trends) * 0.3:
            warnings.append("High market volatility detected")

        # Negative trend warnings
        negative_trends = [p for p in predictions if p.predicted_change_percent < -10]
        if len(negative_trends) > len(predictions) * 0.4:
            warnings.append("Widespread negative price predictions")

        return warnings

    def _calculate_forecast_confidence(self, predictions: List[PredictionResponse]) -> float:
        """Calculate overall forecast confidence."""
        if not predictions:
            return 0.0

        confidences = [p.confidence for p in predictions]
        return sum(confidences) / len(confidences)
