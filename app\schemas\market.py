"""
Pydantic schemas for market data and analysis.
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class MarketDataBase(BaseModel):
    """Base schema for market data."""
    current_price: float = Field(..., description="Current market price")
    price_change_24h: float = Field(0.0, description="24h price change")
    price_change_7d: float = Field(0.0, description="7d price change")
    volume_24h: int = Field(0, description="Trading volume last 24h")
    listings_count: int = Field(0, description="Current active listings")
    avg_listing_time: float = Field(0.0, description="Average time to sell (hours)")
    liquidity_score: float = Field(0.0, description="How liquid the market is (0-1)")
    volatility_score: float = Field(0.0, description="Price volatility (0-1)")
    trend_strength: float = Field(0.0, description="Strength of current trend (0-1)")
    data_quality: float = Field(0.0, description="Quality of data (0-1)")


class MarketDataResponse(MarketDataBase):
    """Schema for market data API responses."""
    id: int
    modifier_id: int
    price_min: Optional[float]
    price_max: Optional[float]
    price_median: Optional[float]
    price_std_dev: Optional[float]
    last_updated: datetime
    update_frequency: int
    created_at: datetime
    
    # Computed properties
    is_trending_up: bool = Field(..., description="Whether price is trending upward")
    is_volatile: bool = Field(..., description="Whether market is volatile")
    
    class Config:
        from_attributes = True


class PriceHistoryBase(BaseModel):
    """Base schema for price history."""
    timestamp: datetime = Field(..., description="Timestamp of the price point")
    price: float = Field(..., description="Price at this timestamp")
    volume: int = Field(0, description="Trading volume")
    listings_count: int = Field(0, description="Number of listings")
    avg_listing_price: Optional[float] = Field(None, description="Average listing price")
    market_cap: Optional[float] = Field(None, description="Total value of all listings")


class PriceHistoryResponse(PriceHistoryBase):
    """Schema for price history API responses."""
    id: int
    market_data_id: int
    moving_avg_7d: Optional[float]
    moving_avg_30d: Optional[float]
    rsi: Optional[float]
    bollinger_upper: Optional[float]
    bollinger_lower: Optional[float]
    league_day: Optional[int]
    patch_version: Optional[str]
    meta_score: Optional[float]
    data_source: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True


class MarketTrendBase(BaseModel):
    """Base schema for market trends."""
    trend_type: str = Field(..., description="bullish, bearish, or sideways")
    trend_strength: float = Field(0.0, description="Strength of trend (0-1)")
    trend_duration: int = Field(0, description="Days trend has been active")
    predicted_price_1d: Optional[float] = Field(None, description="1-day price prediction")
    predicted_price_7d: Optional[float] = Field(None, description="7-day price prediction")
    predicted_price_30d: Optional[float] = Field(None, description="30-day price prediction")
    prediction_confidence: float = Field(0.0, description="Confidence in predictions")
    pattern_type: Optional[str] = Field(None, description="Detected chart pattern")
    support_level: Optional[float] = Field(None, description="Support price level")
    resistance_level: Optional[float] = Field(None, description="Resistance price level")
    breakout_probability: float = Field(0.0, description="Probability of breakout")


class MarketTrendResponse(MarketTrendBase):
    """Schema for market trend API responses."""
    id: int
    modifier_id: int
    volatility_forecast: float
    downside_risk: float
    upside_potential: float
    analysis_date: datetime
    model_version: Optional[str]
    data_points_used: Optional[int]
    market_sentiment: Optional[str]
    seasonal_factor: float
    correlation_factors: Optional[Dict[str, Any]]
    
    # Computed properties
    is_bullish: bool = Field(..., description="Whether trend is bullish")
    has_high_confidence: bool = Field(..., description="Whether predictions have high confidence")
    
    class Config:
        from_attributes = True


class MarketOverview(BaseModel):
    """Schema for market overview dashboard data."""
    total_modifiers_tracked: int
    active_markets: int
    trending_up_count: int
    trending_down_count: int
    high_volatility_count: int
    
    # Price statistics
    average_price_change_24h: float
    average_price_change_7d: float
    highest_price_change_24h: float
    lowest_price_change_24h: float
    
    # Volume statistics
    total_volume_24h: int
    average_volume_24h: float
    highest_volume_modifier: Optional[str]
    
    # Market health indicators
    overall_market_sentiment: str = Field(..., description="bullish, bearish, or neutral")
    market_volatility_index: float = Field(..., description="Overall market volatility (0-1)")
    liquidity_index: float = Field(..., description="Overall market liquidity (0-1)")
    
    # Recent alerts
    significant_price_changes: List[Dict[str, Any]]
    new_trending_modifiers: List[Dict[str, Any]]
    
    last_updated: datetime


class PriceAlert(BaseModel):
    """Schema for price change alerts."""
    modifier_id: int
    modifier_name: str
    old_price: float
    new_price: float
    price_change_percent: float
    volume_change_percent: float
    alert_timestamp: datetime
    alert_type: str = Field(..., description="spike, drop, or volatility")
    severity: str = Field(..., description="low, medium, high, or critical")


class ArbitrageOpportunity(BaseModel):
    """Schema for arbitrage opportunities."""
    modifier_id: int
    modifier_name: str
    buy_price: float
    sell_price: float
    profit_margin: float
    profit_amount: float
    confidence_score: float
    risk_level: str = Field(..., description="low, medium, or high")
    estimated_time_to_profit: float = Field(..., description="Hours to realize profit")
    market_depth: int = Field(..., description="Number of available opportunities")
    last_updated: datetime


class VolatileModifier(BaseModel):
    """Schema for high volatility modifiers."""
    modifier_id: int
    modifier_name: str
    current_price: float
    volatility_score: float
    price_range_24h: Dict[str, float] = Field(..., description="min and max prices in 24h")
    volume_spike: bool = Field(..., description="Whether there's unusual volume")
    risk_warning: str = Field(..., description="Risk assessment for trading")
    opportunity_score: float = Field(..., description="Potential opportunity score (0-1)")
    
    
class MarketCorrelation(BaseModel):
    """Schema for market correlation analysis."""
    modifier_pairs: List[Dict[str, Any]]
    correlation_matrix: Dict[str, Dict[str, float]]
    strongest_correlations: List[Dict[str, Any]]
    weakest_correlations: List[Dict[str, Any]]
    analysis_period_days: int
    confidence_level: float
