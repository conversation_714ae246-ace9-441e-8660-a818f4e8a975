"""
API endpoint tests for PoE 2 Tablet Optimizer.
"""
import pytest
import json
from fastapi import status


class TestTabletAPI:
    """Test tablet-related API endpoints."""
    
    def test_get_tablet_types(self, client, sample_tablet_type):
        """Test getting tablet types."""
        response = client.get("/tablets/types")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) >= 1
        assert data[0]["name"] == sample_tablet_type.name
        assert data[0]["display_name"] == sample_tablet_type.display_name
    
    def test_evaluate_tablet_success(self, client, sample_tablet_data, sample_tablet_type, sample_modifier):
        """Test successful tablet evaluation."""
        response = client.post("/tablets/evaluate", json=sample_tablet_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check required fields
        assert "total_value" in data
        assert "profit_estimate" in data
        assert "recommendation" in data
        assert "confidence_score" in data
        assert "breakdown" in data
        
        # Check data types
        assert isinstance(data["total_value"], (int, float))
        assert isinstance(data["profit_estimate"], (int, float))
        assert isinstance(data["confidence_score"], (int, float))
        assert isinstance(data["breakdown"], list)
    
    def test_evaluate_tablet_invalid_data(self, client):
        """Test tablet evaluation with invalid data."""
        invalid_data = {
            "tablet_type_id": 999,  # Non-existent ID
            "modifier1_id": 999,    # Non-existent ID
        }
        
        response = client.post("/tablets/evaluate", json=invalid_data)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_evaluate_tablet_missing_required_fields(self, client):
        """Test tablet evaluation with missing required fields."""
        incomplete_data = {
            "modifier1_id": 1
            # Missing tablet_type_id
        }
        
        response = client.post("/tablets/evaluate", json=incomplete_data)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestModifierAPI:
    """Test modifier-related API endpoints."""
    
    def test_get_modifiers(self, client, sample_modifier):
        """Test getting modifiers."""
        response = client.get("/modifiers")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) >= 1
        
        modifier = data[0]
        assert modifier["name"] == sample_modifier.name
        assert modifier["display_name"] == sample_modifier.display_name
        assert modifier["category"] == sample_modifier.category
    
    def test_get_modifiers_by_tablet_type(self, client, sample_modifier, sample_tablet_type):
        """Test getting modifiers filtered by tablet type."""
        response = client.get(f"/modifiers?tablet_type_id={sample_tablet_type.id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) >= 1
        assert all(mod["tablet_type_id"] == sample_tablet_type.id for mod in data)
    
    def test_get_modifier_by_id(self, client, sample_modifier):
        """Test getting a specific modifier by ID."""
        response = client.get(f"/modifiers/{sample_modifier.id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == sample_modifier.id
        assert data["name"] == sample_modifier.name
    
    def test_get_modifier_not_found(self, client):
        """Test getting a non-existent modifier."""
        response = client.get("/modifiers/999")
        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestMarketAPI:
    """Test market-related API endpoints."""
    
    def test_get_market_overview(self, client, sample_market_data):
        """Test getting market overview."""
        response = client.get("/market/overview")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check required fields
        assert "total_modifiers_tracked" in data
        assert "trending_up_count" in data
        assert "trending_down_count" in data
        assert "high_volatility_count" in data
        assert "average_change_24h" in data
        assert "last_updated" in data
    
    def test_get_trending_modifiers(self, client, sample_market_data):
        """Test getting trending modifiers."""
        response = client.get("/market/trending")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        
        if data:  # If there are trending modifiers
            modifier = data[0]
            assert "modifier_id" in modifier
            assert "modifier_name" in modifier
            assert "price_change_24h" in modifier
            assert "current_price" in modifier
    
    def test_get_price_history(self, client, sample_modifier):
        """Test getting price history for a modifier."""
        response = client.get(f"/market/price-history/{sample_modifier.id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
    
    def test_refresh_market_data(self, client, mock_poe_api):
        """Test refreshing market data."""
        response = client.post("/market/refresh")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
        assert "updated_count" in data


class TestAnalyticsAPI:
    """Test analytics-related API endpoints."""
    
    def test_get_analytics_overview(self, client, sample_market_data):
        """Test getting analytics overview."""
        response = client.get("/analytics/overview")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Check required fields
        assert "market_sentiment" in data
        assert "volatility_analysis" in data
        assert "profit_opportunities" in data
        assert "risk_assessment" in data
    
    def test_get_predictions(self, client, sample_modifier):
        """Test getting price predictions."""
        response = client.get(f"/analytics/predictions/{sample_modifier.id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "modifier_id" in data
        assert "predictions" in data
        assert "confidence" in data
        assert "model_accuracy" in data
    
    def test_get_profit_opportunities(self, client, sample_market_data):
        """Test getting profit opportunities."""
        response = client.get("/analytics/opportunities")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        
        if data:  # If there are opportunities
            opportunity = data[0]
            assert "modifier_id" in opportunity
            assert "profit_estimate" in opportunity
            assert "confidence" in opportunity


class TestRegexAPI:
    """Test regex generation API endpoints."""
    
    def test_generate_regex_patterns(self, client, sample_modifier):
        """Test generating regex patterns."""
        request_data = {
            "modifier_ids": [sample_modifier.id],
            "search_strategy": "comprehensive",
            "include_tiers": True
        }
        
        response = client.post("/regex/generate", json=request_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "patterns" in data
        assert "search_strategy" in data
        assert isinstance(data["patterns"], list)
        
        if data["patterns"]:
            pattern = data["patterns"][0]
            assert "pattern" in pattern
            assert "description" in pattern
    
    def test_validate_regex_pattern(self, client):
        """Test validating a regex pattern."""
        request_data = {
            "pattern": r"increased.*damage",
            "test_strings": ["increased damage", "increased fire damage", "reduced damage"]
        }
        
        response = client.post("/regex/validate", json=request_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "is_valid" in data
        assert "matches" in data
        assert isinstance(data["matches"], list)


class TestErrorHandling:
    """Test error handling across all endpoints."""
    
    def test_404_not_found(self, client):
        """Test 404 error handling."""
        response = client.get("/nonexistent-endpoint")
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_405_method_not_allowed(self, client):
        """Test 405 error handling."""
        response = client.delete("/tablets/types")  # DELETE not allowed
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
    
    def test_422_validation_error(self, client):
        """Test 422 validation error handling."""
        response = client.post("/tablets/evaluate", json={"invalid": "data"})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        data = response.json()
        assert "detail" in data


class TestPerformance:
    """Test API performance."""
    
    def test_response_time_tablet_evaluation(self, client, sample_tablet_data, performance_timer):
        """Test tablet evaluation response time."""
        performance_timer.start()
        response = client.post("/tablets/evaluate", json=sample_tablet_data)
        performance_timer.stop()
        
        assert response.status_code == status.HTTP_200_OK
        assert performance_timer.elapsed < 2.0  # Should respond within 2 seconds
    
    def test_response_time_market_overview(self, client, performance_timer):
        """Test market overview response time."""
        performance_timer.start()
        response = client.get("/market/overview")
        performance_timer.stop()
        
        assert response.status_code == status.HTTP_200_OK
        assert performance_timer.elapsed < 1.0  # Should respond within 1 second
    
    def test_concurrent_requests(self, client, sample_tablet_data):
        """Test handling concurrent requests."""
        import concurrent.futures
        import threading
        
        def make_request():
            return client.post("/tablets/evaluate", json=sample_tablet_data)
        
        # Make 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            responses = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should succeed
        assert all(response.status_code == status.HTTP_200_OK for response in responses)


class TestSecurity:
    """Test security aspects of the API."""
    
    def test_sql_injection_protection(self, client):
        """Test protection against SQL injection."""
        malicious_input = "1'; DROP TABLE modifiers; --"
        
        response = client.get(f"/modifiers/{malicious_input}")
        
        # Should return 422 (validation error) or 404, not 500 (server error)
        assert response.status_code in [status.HTTP_422_UNPROCESSABLE_ENTITY, status.HTTP_404_NOT_FOUND]
    
    def test_xss_protection(self, client):
        """Test protection against XSS attacks."""
        malicious_script = "<script>alert('xss')</script>"
        
        # Try to inject script in various endpoints
        response = client.get(f"/modifiers?search={malicious_script}")
        
        # Should not return the script in response
        assert malicious_script not in response.text
    
    def test_rate_limiting(self, client):
        """Test rate limiting (if implemented)."""
        # Make many requests quickly
        responses = []
        for _ in range(100):
            response = client.get("/market/overview")
            responses.append(response)
            
            # If rate limiting is implemented, we should get 429 at some point
            if response.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
                break
        
        # This test passes regardless of rate limiting implementation
        # It's more of a documentation of expected behavior
        assert True
