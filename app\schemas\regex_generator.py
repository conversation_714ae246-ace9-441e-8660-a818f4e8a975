"""
Pydantic schemas for regex pattern generation.
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class SearchStrategy(str, Enum):
    """Search strategy options."""
    EXACT = "exact"
    FUZZY = "fuzzy"
    BROAD = "broad"
    NARROW = "narrow"


class PatternComplexity(str, Enum):
    """Pattern complexity levels."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"


class RegexPatternBase(BaseModel):
    """Base schema for regex patterns."""
    pattern: str = Field(..., description="The regex pattern")
    description: str = Field(..., description="Human-readable description")
    priority: int = Field(1, description="Pattern priority (1-10)")
    complexity: PatternComplexity = Field(PatternComplexity.SIMPLE, description="Pattern complexity")
    estimated_matches: Optional[int] = Field(None, description="Estimated number of matches")


class RegexPattern(RegexPatternBase):
    """Schema for regex pattern responses."""
    id: Optional[int] = None
    modifier_names: List[str] = Field(..., description="Modifier names this pattern matches")
    test_cases: List[str] = Field(..., description="Test cases for validation")
    false_positive_rate: Optional[float] = Field(None, description="Estimated false positive rate")
    performance_score: Optional[float] = Field(None, description="Pattern performance score")
    created_at: Optional[datetime] = None


class PatternGenerationRequest(BaseModel):
    """Schema for pattern generation requests."""
    modifier_ids: List[int] = Field(..., description="Modifier IDs to generate patterns for")
    search_strategy: SearchStrategy = Field(SearchStrategy.EXACT, description="Search strategy")
    max_patterns: int = Field(10, description="Maximum number of patterns to generate")
    include_synonyms: bool = Field(True, description="Include synonym variations")
    include_abbreviations: bool = Field(True, description="Include abbreviations")
    case_sensitive: bool = Field(False, description="Generate case-sensitive patterns")
    word_boundaries: bool = Field(True, description="Use word boundaries")
    allow_partial_matches: bool = Field(False, description="Allow partial word matches")


class PatternOptimization(BaseModel):
    """Schema for pattern optimization results."""
    original_pattern: str
    optimized_pattern: str
    optimization_type: str = Field(..., description="Type of optimization applied")
    performance_improvement: float = Field(..., description="Performance improvement percentage")
    accuracy_change: float = Field(..., description="Change in accuracy")
    complexity_reduction: float = Field(..., description="Complexity reduction percentage")
    explanation: str = Field(..., description="Explanation of optimization")


class PatternValidationResult(BaseModel):
    """Schema for pattern validation results."""
    pattern: str
    is_valid: bool
    syntax_errors: List[str] = Field(default_factory=list)
    performance_warnings: List[str] = Field(default_factory=list)
    test_results: Dict[str, bool] = Field(..., description="Test case results")
    match_count: int = Field(..., description="Number of successful matches")
    false_positives: List[str] = Field(default_factory=list)
    false_negatives: List[str] = Field(default_factory=list)
    accuracy_score: float = Field(..., description="Pattern accuracy (0-1)")
    precision_score: float = Field(..., description="Pattern precision (0-1)")
    recall_score: float = Field(..., description="Pattern recall (0-1)")


class SearchConfiguration(BaseModel):
    """Schema for search configuration."""
    patterns: List[RegexPattern]
    search_strategy: SearchStrategy
    case_sensitive: bool = False
    use_word_boundaries: bool = True
    max_results: int = 100
    timeout_seconds: int = 30
    highlight_matches: bool = True
    return_context: bool = True
    context_lines: int = 2


class SearchResult(BaseModel):
    """Schema for search results."""
    pattern_used: str
    matches_found: int
    execution_time_ms: int
    results: List[Dict[str, Any]] = Field(..., description="Individual match results")
    performance_metrics: Dict[str, float] = Field(..., description="Search performance metrics")
    warnings: List[str] = Field(default_factory=list)


class PatternLibrary(BaseModel):
    """Schema for pattern library management."""
    name: str = Field(..., description="Library name")
    description: str = Field(..., description="Library description")
    patterns: List[RegexPattern]
    tags: List[str] = Field(default_factory=list)
    version: str = Field("1.0", description="Library version")
    author: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    usage_count: int = Field(0, description="Number of times used")
    success_rate: float = Field(0.0, description="Overall success rate")


class PatternAnalytics(BaseModel):
    """Schema for pattern usage analytics."""
    pattern_id: Optional[int] = None
    pattern: str
    usage_count: int
    success_rate: float
    average_execution_time_ms: float
    most_common_matches: List[str]
    error_rate: float
    last_used: Optional[datetime]
    performance_trend: str = Field(..., description="improving, stable, or declining")
    recommendations: List[str] = Field(..., description="Optimization recommendations")
