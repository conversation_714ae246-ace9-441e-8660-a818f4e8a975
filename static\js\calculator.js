/**
 * Manual Tablet Calculator JavaScript
 */

const Calculator = {
    // Current calculation state
    currentCalculation: null,
    savedCalculations: [],
    
    // Initialize calculator
    init() {
        this.loadTabletTypes();
        this.setupEventListeners();
        this.loadSavedCalculations();
        console.log('Calculator initialized');
    },
    
    // Setup event listeners
    setupEventListeners() {
        // Form submission
        document.getElementById('calculatorForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.calculateValue();
        });
        
        // Tablet type change
        document.getElementById('tabletType').addEventListener('change', (e) => {
            this.loadModifiersForTabletType(e.target.value);
        });
        
        // Modifier changes
        document.getElementById('modifier1').addEventListener('change', (e) => {
            this.updateModifierInfo(1, e.target.value);
        });
        
        document.getElementById('modifier2').addEventListener('change', (e) => {
            this.updateModifierInfo(2, e.target.value);
        });
        
        // Tier changes
        document.getElementById('tier1').addEventListener('change', () => {
            this.updateCalculationPreview();
        });
        
        document.getElementById('tier2').addEventListener('change', () => {
            this.updateCalculationPreview();
        });
    },
    
    // Load tablet types
    async loadTabletTypes() {
        try {
            const tabletTypes = await App.apiCall('/tablets/types');
            const select = document.getElementById('tabletType');
            
            select.innerHTML = '<option value="">Select tablet type...</option>';
            tabletTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id;
                option.textContent = type.display_name;
                option.dataset.baseCost = type.base_cost;
                option.dataset.craftingCost = type.crafting_cost;
                select.appendChild(option);
            });
        } catch (error) {
            App.showNotification('Failed to load tablet types', 'error');
        }
    },
    
    // Load modifiers for selected tablet type
    async loadModifiersForTabletType(tabletTypeId) {
        if (!tabletTypeId) {
            this.clearModifierSelects();
            return;
        }
        
        try {
            const modifiers = await App.apiCall(`/modifiers?tablet_type_id=${tabletTypeId}`);
            this.populateModifierSelects(modifiers);
        } catch (error) {
            App.showNotification('Failed to load modifiers', 'error');
        }
    },
    
    // Populate modifier select elements
    populateModifierSelects(modifiers) {
        const modifier1Select = document.getElementById('modifier1');
        const modifier2Select = document.getElementById('modifier2');
        
        // Clear existing options
        modifier1Select.innerHTML = '<option value="">Select modifier...</option>';
        modifier2Select.innerHTML = '<option value="">Select modifier...</option>';
        
        // Add modifiers
        modifiers.forEach(modifier => {
            const option1 = document.createElement('option');
            option1.value = modifier.id;
            option1.textContent = modifier.display_name;
            option1.dataset.baseValue = modifier.base_value;
            option1.dataset.currentValue = modifier.current_value;
            option1.dataset.category = modifier.category;
            option1.dataset.isValuable = modifier.is_valuable;
            
            const option2 = option1.cloneNode(true);
            
            modifier1Select.appendChild(option1);
            modifier2Select.appendChild(option2);
        });
    },
    
    // Clear modifier selects
    clearModifierSelects() {
        document.getElementById('modifier1').innerHTML = '<option value="">Select modifier...</option>';
        document.getElementById('modifier2').innerHTML = '<option value="">Select modifier...</option>';
        this.clearModifierInfo();
    },
    
    // Update modifier info display
    updateModifierInfo(modifierNumber, modifierId) {
        const select = document.getElementById(`modifier${modifierNumber}`);
        const selectedOption = select.options[select.selectedIndex];
        
        if (selectedOption && selectedOption.value) {
            document.getElementById(`mod${modifierNumber}BaseValue`).textContent = 
                parseFloat(selectedOption.dataset.currentValue || 0).toFixed(2);
            document.getElementById(`mod${modifierNumber}Category`).textContent = 
                selectedOption.dataset.category || '-';
        } else {
            document.getElementById(`mod${modifierNumber}BaseValue`).textContent = '-';
            document.getElementById(`mod${modifierNumber}Category`).textContent = '-';
        }
        
        this.updateCalculationPreview();
    },
    
    // Clear modifier info
    clearModifierInfo() {
        ['mod1BaseValue', 'mod1Category', 'mod2BaseValue', 'mod2Category'].forEach(id => {
            document.getElementById(id).textContent = '-';
        });
    },
    
    // Update calculation preview (real-time)
    updateCalculationPreview() {
        const modifier1 = document.getElementById('modifier1').value;
        const modifier2 = document.getElementById('modifier2').value;
        
        if (modifier1) {
            // Show quick preview without full calculation
            this.showQuickPreview();
        }
    },
    
    // Show quick preview
    showQuickPreview() {
        // Simple preview calculation
        const mod1Select = document.getElementById('modifier1');
        const mod2Select = document.getElementById('modifier2');
        const tier1 = parseInt(document.getElementById('tier1').value);
        const tier2 = parseInt(document.getElementById('tier2').value);
        
        let totalValue = 0;
        
        if (mod1Select.value) {
            const mod1Value = parseFloat(mod1Select.options[mod1Select.selectedIndex].dataset.currentValue || 0);
            totalValue += mod1Value * (1 + (tier1 - 1) * 0.2); // Simple tier multiplier
        }
        
        if (mod2Select.value) {
            const mod2Value = parseFloat(mod2Select.options[mod2Select.selectedIndex].dataset.currentValue || 0);
            totalValue += mod2Value * (1 + (tier2 - 1) * 0.2);
        }
        
        // Show in form validation or small preview
        if (totalValue > 0) {
            const previewElement = document.getElementById('quickPreview');
            if (previewElement) {
                previewElement.textContent = `~${totalValue.toFixed(2)} ex`;
            }
        }
    },
    
    // Main calculation function
    async calculateValue() {
        const formData = this.getFormData();
        
        if (!this.validateForm(formData)) {
            return;
        }
        
        try {
            App.setLoading('calculatorForm', true);
            
            // Call valuation API
            const result = await App.apiCall('/tablets/evaluate', {
                method: 'POST',
                body: JSON.stringify(formData)
            });
            
            this.currentCalculation = {
                ...formData,
                result: result,
                timestamp: new Date().toISOString()
            };
            
            this.displayResults(result);
            this.createValueVisualization(result);
            
        } catch (error) {
            App.showNotification('Calculation failed: ' + error.message, 'error');
        } finally {
            App.setLoading('calculatorForm', false);
        }
    },
    
    // Get form data
    getFormData() {
        return {
            tablet_type_id: parseInt(document.getElementById('tabletType').value),
            modifier1_id: document.getElementById('modifier1').value ? 
                parseInt(document.getElementById('modifier1').value) : null,
            modifier2_id: document.getElementById('modifier2').value ? 
                parseInt(document.getElementById('modifier2').value) : null,
            modifier1_tier: parseInt(document.getElementById('tier1').value),
            modifier2_tier: parseInt(document.getElementById('tier2').value),
            market_condition: document.getElementById('marketCondition').value,
            time_horizon: document.getElementById('timeHorizon').value,
            risk_tolerance: document.getElementById('riskTolerance').value,
            include_predictions: document.getElementById('includePredictions').checked
        };
    },
    
    // Validate form
    validateForm(formData) {
        if (!formData.tablet_type_id) {
            App.showNotification('Please select a tablet type', 'warning');
            return false;
        }
        
        if (!formData.modifier1_id) {
            App.showNotification('Please select at least one modifier', 'warning');
            return false;
        }
        
        return true;
    },
    
    // Display calculation results
    displayResults(result) {
        // Show result panels
        document.getElementById('quickSummary').style.display = 'block';
        document.getElementById('detailedResults').style.display = 'block';
        document.getElementById('visualizationCard').style.display = 'block';
        
        // Update quick summary
        document.getElementById('totalValue').textContent = result.total_value.toFixed(2);
        document.getElementById('profitEstimate').textContent = result.profit_estimate.toFixed(2);
        
        const recommendationElement = document.getElementById('recommendation');
        recommendationElement.textContent = this.formatRecommendation(result.recommendation);
        recommendationElement.className = this.getRecommendationClass(result.recommendation);
        
        // Update detailed breakdown
        this.updateValueBreakdown(result);
        this.updateSynergySection(result);
        this.updateRiskAssessment(result);
        this.updateMarketContext(result);
        
        // Scroll to results
        document.getElementById('quickSummary').scrollIntoView({ behavior: 'smooth' });
    },
    
    // Update value breakdown table
    updateValueBreakdown(result) {
        const tbody = document.getElementById('valueBreakdown');
        tbody.innerHTML = '';
        
        if (result.breakdown) {
            result.breakdown.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.component}</td>
                    <td class="text-end">${item.final_value.toFixed(2)} ex</td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // Add total row
        const totalRow = document.createElement('tr');
        totalRow.className = 'table-active fw-bold';
        totalRow.innerHTML = `
            <td>Total Value</td>
            <td class="text-end">${result.total_value.toFixed(2)} ex</td>
        `;
        tbody.appendChild(totalRow);
        
        // Add cost and profit rows
        const costRow = document.createElement('tr');
        costRow.innerHTML = `
            <td>Total Cost</td>
            <td class="text-end text-danger">-${result.total_cost.toFixed(2)} ex</td>
        `;
        tbody.appendChild(costRow);
        
        const profitRow = document.createElement('tr');
        profitRow.className = result.profit_estimate >= 0 ? 'text-success' : 'text-danger';
        profitRow.innerHTML = `
            <td><strong>Profit Estimate</strong></td>
            <td class="text-end"><strong>${result.profit_estimate >= 0 ? '+' : ''}${result.profit_estimate.toFixed(2)} ex</strong></td>
        `;
        tbody.appendChild(profitRow);
    },
    
    // Update synergy section
    updateSynergySection(result) {
        const synergySection = document.getElementById('synergySection');
        const synergyInfo = document.getElementById('synergyInfo');
        
        if (result.synergy_bonus && result.synergy_bonus > 0) {
            synergySection.style.display = 'block';
            synergyInfo.innerHTML = `
                <strong>Synergy Bonus:</strong> +${result.synergy_bonus.toFixed(2)} ex<br>
                <small>These modifiers work well together, providing additional value beyond their individual worth.</small>
            `;
        } else {
            synergySection.style.display = 'none';
        }
    },
    
    // Update risk assessment
    updateRiskAssessment(result) {
        const riskLevel = document.getElementById('riskLevel');
        const confidence = document.getElementById('confidence');
        const riskFactors = document.getElementById('riskFactors');
        
        riskLevel.textContent = result.risk_level || 'Unknown';
        riskLevel.className = `badge bg-${this.getRiskLevelClass(result.risk_level)}`;
        
        confidence.textContent = `${(result.confidence_score * 100).toFixed(1)}%`;
        
        if (result.risk_factors && result.risk_factors.length > 0) {
            riskFactors.textContent = 'Risk factors: ' + result.risk_factors.join(', ');
        } else {
            riskFactors.textContent = 'No significant risk factors identified.';
        }
    },
    
    // Update market context
    updateMarketContext(result) {
        const marketContext = document.getElementById('marketContext');
        
        const contextHtml = `
            <div class="row">
                <div class="col-md-6">
                    <small><strong>Market Confidence:</strong> ${(result.confidence_score * 100).toFixed(1)}%</small>
                </div>
                <div class="col-md-6">
                    <small><strong>Data Quality:</strong> ${result.data_quality || 'Good'}</small>
                </div>
            </div>
            <div class="mt-2">
                <small class="text-muted">
                    Calculation based on current market data and historical trends. 
                    Values may fluctuate based on market conditions.
                </small>
            </div>
        `;
        
        marketContext.innerHTML = contextHtml;
    },
    
    // Create value visualization
    createValueVisualization(result) {
        if (!result.breakdown) return;
        
        const labels = result.breakdown.map(item => item.component);
        const values = result.breakdown.map(item => item.final_value);
        
        const config = {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: [
                        Charts.colors.primary,
                        Charts.colors.success,
                        Charts.colors.warning,
                        Charts.colors.purple,
                        Charts.colors.teal
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${context.label}: ${value.toFixed(2)} ex (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        };
        
        Charts.createChart('valueChart', config);
    },
    
    // Helper functions
    formatRecommendation(recommendation) {
        const recommendations = {
            'strong_buy': 'Strong Buy',
            'buy': 'Buy',
            'weak_buy': 'Weak Buy',
            'hold': 'Hold',
            'sell': 'Sell'
        };
        return recommendations[recommendation] || recommendation;
    },
    
    getRecommendationClass(recommendation) {
        const classes = {
            'strong_buy': 'text-success fw-bold',
            'buy': 'text-success',
            'weak_buy': 'text-info',
            'hold': 'text-warning',
            'sell': 'text-danger'
        };
        return classes[recommendation] || 'text-muted';
    },
    
    getRiskLevelClass(riskLevel) {
        const classes = {
            'low': 'success',
            'medium': 'warning',
            'high': 'danger'
        };
        return classes[riskLevel] || 'secondary';
    },
    
    // Save calculation
    saveCalculation() {
        if (!this.currentCalculation) {
            App.showNotification('No calculation to save', 'warning');
            return;
        }
        
        const name = prompt('Enter a name for this calculation:');
        if (!name) return;
        
        const savedCalc = {
            ...this.currentCalculation,
            name: name,
            id: Date.now()
        };
        
        this.savedCalculations.push(savedCalc);
        localStorage.setItem('savedCalculations', JSON.stringify(this.savedCalculations));
        
        App.showNotification('Calculation saved successfully', 'success');
    },
    
    // Load saved calculations
    loadSavedCalculations() {
        const saved = localStorage.getItem('savedCalculations');
        if (saved) {
            this.savedCalculations = JSON.parse(saved);
        }
    },
    
    // Show saved calculations modal
    showSavedCalculations() {
        const modal = new bootstrap.Modal(document.getElementById('savedCalculationsModal'));
        const content = document.getElementById('savedCalculationsContent');
        
        if (this.savedCalculations.length === 0) {
            content.innerHTML = '<p class="text-muted">No saved calculations found.</p>';
        } else {
            content.innerHTML = this.savedCalculations.map(calc => `
                <div class="card mb-3">
                    <div class="card-body">
                        <h6>${calc.name}</h6>
                        <p class="text-muted mb-2">
                            Saved: ${new Date(calc.timestamp).toLocaleString()}<br>
                            Value: ${calc.result.total_value.toFixed(2)} ex | 
                            Profit: ${calc.result.profit_estimate.toFixed(2)} ex
                        </p>
                        <button class="btn btn-sm btn-primary" onclick="Calculator.loadCalculation(${calc.id})">
                            Load
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="Calculator.deleteCalculation(${calc.id})">
                            Delete
                        </button>
                    </div>
                </div>
            `).join('');
        }
        
        modal.show();
    },
    
    // Load specific calculation
    loadCalculation(id) {
        const calc = this.savedCalculations.find(c => c.id === id);
        if (!calc) return;
        
        // Populate form with saved data
        document.getElementById('tabletType').value = calc.tablet_type_id;
        this.loadModifiersForTabletType(calc.tablet_type_id).then(() => {
            if (calc.modifier1_id) document.getElementById('modifier1').value = calc.modifier1_id;
            if (calc.modifier2_id) document.getElementById('modifier2').value = calc.modifier2_id;
            document.getElementById('tier1').value = calc.modifier1_tier;
            document.getElementById('tier2').value = calc.modifier2_tier;
            
            // Update modifier info
            this.updateModifierInfo(1, calc.modifier1_id);
            this.updateModifierInfo(2, calc.modifier2_id);
            
            // Display results
            this.displayResults(calc.result);
            this.createValueVisualization(calc.result);
        });
        
        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('savedCalculationsModal')).hide();
        App.showNotification('Calculation loaded', 'success');
    },
    
    // Delete calculation
    deleteCalculation(id) {
        this.savedCalculations = this.savedCalculations.filter(c => c.id !== id);
        localStorage.setItem('savedCalculations', JSON.stringify(this.savedCalculations));
        this.showSavedCalculations(); // Refresh modal
        App.showNotification('Calculation deleted', 'success');
    }
};

// Global functions for HTML onclick handlers
window.resetForm = function() {
    document.getElementById('calculatorForm').reset();
    Calculator.clearModifierInfo();
    document.getElementById('quickSummary').style.display = 'none';
    document.getElementById('detailedResults').style.display = 'none';
    document.getElementById('visualizationCard').style.display = 'none';
};

window.loadExample = function() {
    // Load a predefined example
    document.getElementById('tabletType').value = '1'; // Assuming first tablet type
    Calculator.loadModifiersForTabletType('1').then(() => {
        document.getElementById('modifier1').value = '1';
        document.getElementById('tier1').value = '3';
        Calculator.updateModifierInfo(1, '1');
    });
};

window.saveCalculation = () => Calculator.saveCalculation();
window.loadSavedCalculations = () => Calculator.showSavedCalculations();
window.clearSavedCalculations = function() {
    if (confirm('Are you sure you want to clear all saved calculations?')) {
        Calculator.savedCalculations = [];
        localStorage.removeItem('savedCalculations');
        Calculator.showSavedCalculations();
        App.showNotification('All calculations cleared', 'success');
    }
};

window.compareWithSimilar = function() {
    App.showNotification('Comparison feature coming soon', 'info');
};

window.generateRegexForThis = function() {
    App.showNotification('Regex generation feature coming soon', 'info');
};

window.exportCalculation = function() {
    if (!Calculator.currentCalculation) {
        App.showNotification('No calculation to export', 'warning');
        return;
    }
    
    const data = JSON.stringify(Calculator.currentCalculation, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tablet_calculation_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Calculator.init();
});
