"""
Pydantic schemas for analytics and predictions.
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class PredictionModelBase(BaseModel):
    """Base schema for prediction models."""
    name: str = Field(..., description="Model name")
    model_type: str = Field(..., description="Type of ML model")
    version: str = Field(..., description="Model version")
    description: Optional[str] = Field(None, description="Model description")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Model hyperparameters")
    features: Optional[List[str]] = Field(None, description="List of features used")
    target_variable: Optional[str] = Field(None, description="What the model predicts")


class PredictionModelResponse(PredictionModelBase):
    """Schema for prediction model API responses."""
    id: int
    accuracy_score: float
    precision_score: float
    recall_score: float
    f1_score: float
    mse: float
    mae: float
    r2_score: float
    training_data_size: int
    training_date_from: Optional[datetime]
    training_date_to: Optional[datetime]
    validation_split: float
    is_active: bool
    is_production: bool
    last_retrained: Optional[datetime]
    next_retrain_due: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: Optional[str]
    
    # Computed properties
    performance_summary: Dict[str, float] = Field(..., description="Summary of model performance")
    
    class Config:
        from_attributes = True


class TrendAnalysisBase(BaseModel):
    """Base schema for trend analysis."""
    analysis_date: datetime = Field(..., description="When the analysis was performed")
    period_start: datetime = Field(..., description="Start of analysis period")
    period_end: datetime = Field(..., description="End of analysis period")
    trend_direction: Optional[str] = Field(None, description="up, down, or sideways")
    trend_strength: float = Field(0.0, description="Strength of trend (0-1)")
    trend_confidence: float = Field(0.0, description="Model confidence")
    forecast_horizon_days: int = Field(30, description="Number of days forecasted")


class TrendAnalysisResponse(TrendAnalysisBase):
    """Schema for trend analysis API responses."""
    id: int
    model_id: int
    modifier_id: int
    change_points: Optional[List[Dict[str, Any]]] = Field(None, description="Detected trend change points")
    predicted_prices: Optional[Dict[str, float]] = Field(None, description="Time series of predicted prices")
    prediction_intervals: Optional[Dict[str, Dict[str, float]]] = Field(None, description="Confidence intervals")
    volatility_forecast: float
    correlation_score: float
    seasonality_detected: bool
    seasonal_pattern: Optional[Dict[str, Any]]
    momentum_indicator: float
    mean_reversion_score: float
    breakout_probability: float
    support_resistance_levels: Optional[Dict[str, float]]
    feature_importance: Optional[Dict[str, float]]
    model_explanation: Optional[str]
    prediction_accuracy: float
    data_quality_score: float
    outliers_detected: int
    computation_time_ms: int
    created_at: datetime
    
    # Computed properties
    is_reliable: bool = Field(..., description="Whether analysis is reliable")
    recommendation: str = Field(..., description="Trading recommendation")
    
    class Config:
        from_attributes = True


class BuyRecommendation(BaseModel):
    """Schema for buy recommendations."""
    modifier_id: int
    modifier_name: str
    current_price: float
    predicted_price_7d: float
    predicted_price_30d: float
    expected_return_percent: float
    confidence_score: float
    risk_level: str = Field(..., description="low, medium, or high")
    reasoning: str = Field(..., description="Why this is recommended")
    entry_price_range: Dict[str, float] = Field(..., description="Recommended buy price range")
    stop_loss_price: Optional[float] = Field(None, description="Recommended stop loss")
    take_profit_price: Optional[float] = Field(None, description="Recommended take profit")
    time_horizon_days: int = Field(..., description="Recommended holding period")
    market_conditions: str = Field(..., description="Current market conditions")


class SellRecommendation(BaseModel):
    """Schema for sell recommendations."""
    modifier_id: int
    modifier_name: str
    current_price: float
    predicted_price_7d: float
    predicted_price_30d: float
    expected_decline_percent: float
    confidence_score: float
    urgency_level: str = Field(..., description="low, medium, high, or urgent")
    reasoning: str = Field(..., description="Why selling is recommended")
    target_sell_price: float = Field(..., description="Recommended sell price")
    time_to_sell_days: int = Field(..., description="Recommended timeframe to sell")
    alternative_actions: List[str] = Field(..., description="Alternative strategies")


class ModelPerformance(BaseModel):
    """Schema for model performance metrics."""
    model_id: int
    model_name: str
    evaluation_period_days: int
    predictions_made: int
    correct_predictions: int
    accuracy_percent: float
    average_error_percent: float
    best_prediction_accuracy: float
    worst_prediction_accuracy: float
    trend_prediction_accuracy: float
    price_prediction_mae: float
    confidence_calibration: float = Field(..., description="How well confidence matches actual accuracy")
    recent_performance_trend: str = Field(..., description="improving, stable, or declining")


class MarketSentiment(BaseModel):
    """Schema for market sentiment analysis."""
    overall_sentiment: str = Field(..., description="bullish, bearish, or neutral")
    sentiment_score: float = Field(..., description="Sentiment score (-1 to 1)")
    confidence_level: float = Field(..., description="Confidence in sentiment analysis")
    
    # Sentiment breakdown
    bullish_indicators: List[str]
    bearish_indicators: List[str]
    neutral_factors: List[str]
    
    # Market metrics
    fear_greed_index: float = Field(..., description="Fear & Greed index (0-100)")
    volatility_sentiment: str = Field(..., description="low, normal, or high")
    volume_sentiment: str = Field(..., description="low, normal, or high")
    
    # Predictions
    sentiment_forecast_7d: str
    sentiment_forecast_30d: str
    key_events_affecting_sentiment: List[str]
    
    analysis_timestamp: datetime


class SeasonalPattern(BaseModel):
    """Schema for seasonal pattern analysis."""
    modifier_id: Optional[int] = None
    pattern_type: str = Field(..., description="daily, weekly, monthly, or yearly")
    pattern_strength: float = Field(..., description="Strength of seasonal pattern (0-1)")
    peak_periods: List[Dict[str, Any]] = Field(..., description="When prices typically peak")
    trough_periods: List[Dict[str, Any]] = Field(..., description="When prices typically bottom")
    seasonal_multipliers: Dict[str, float] = Field(..., description="Seasonal adjustment factors")
    confidence_score: float = Field(..., description="Confidence in pattern detection")
    historical_data_years: int = Field(..., description="Years of data used for analysis")
    next_predicted_peak: Optional[datetime]
    next_predicted_trough: Optional[datetime]
    current_seasonal_phase: str = Field(..., description="Current phase in seasonal cycle")


class CorrelationAnalysis(BaseModel):
    """Schema for correlation analysis between modifiers."""
    modifier_ids: List[int]
    correlation_matrix: Dict[str, Dict[str, float]]
    strongest_positive_correlation: Dict[str, Any]
    strongest_negative_correlation: Dict[str, Any]
    analysis_period_days: int
    statistical_significance: Dict[str, float]
    correlation_stability: Dict[str, float] = Field(..., description="How stable correlations are over time")
    trading_implications: List[str] = Field(..., description="What these correlations mean for trading")
    risk_diversification_score: float = Field(..., description="How well these modifiers diversify risk")


class PredictionResponse(BaseModel):
    """Schema for prediction API responses."""
    modifier_id: int
    modifier_name: str
    current_price: float
    predicted_price_1d: Optional[float] = None
    predicted_price_7d: Optional[float] = None
    predicted_price_30d: Optional[float] = None
    confidence_score: float
    trend_direction: str
    prediction_timestamp: datetime
    model_used: str


class MarketForecast(BaseModel):
    """Schema for market forecast responses."""
    forecast_date: datetime
    forecast_horizon_days: int
    overall_market_trend: str
    confidence_level: float
    key_predictions: List[PredictionResponse]
    market_sentiment: MarketSentiment
    risk_factors: List[str]
    opportunities: List[str]


class OpportunityAlert(BaseModel):
    """Schema for trading opportunity alerts."""
    alert_id: str
    alert_type: str = Field(..., description="buy, sell, or watch")
    modifier_id: int
    modifier_name: str
    current_price: float
    target_price: float
    expected_return_percent: float
    confidence_score: float
    urgency_level: str = Field(..., description="low, medium, high, or urgent")
    reasoning: str
    recommended_action: str
    time_sensitive: bool
    expires_at: Optional[datetime]
    created_at: datetime
