/* Custom styles for PoE 2 Tablet Optimizer */

:root {
    --poe-gold: #c9aa71;
    --poe-blue: #4169e1;
    --poe-red: #dc143c;
    --poe-green: #32cd32;
    --poe-purple: #9370db;
    --poe-orange: #ff8c00;
    --poe-dark: #1a1a1a;
    --poe-darker: #0d0d0d;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    color: var(--poe-gold) !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: var(--poe-gold);
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--poe-dark);
    color: white;
    border-bottom: 2px solid var(--poe-gold);
}

/* Value tier badges */
.value-tier-high {
    background-color: var(--poe-gold);
    color: var(--poe-dark);
}

.value-tier-medium {
    background-color: var(--poe-blue);
    color: white;
}

.value-tier-low {
    background-color: #6c757d;
    color: white;
}

/* Trend indicators */
.trend-up {
    color: var(--poe-green);
}

.trend-down {
    color: var(--poe-red);
}

.trend-stable {
    color: #6c757d;
}

/* Pattern display */
.pattern-code {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    word-break: break-all;
}

/* Buttons */
.btn-primary {
    background-color: var(--poe-blue);
    border-color: var(--poe-blue);
}

.btn-primary:hover {
    background-color: #365bb0;
    border-color: #365bb0;
}

.btn-success {
    background-color: var(--poe-green);
    border-color: var(--poe-green);
}

.btn-success:hover {
    background-color: #28a428;
    border-color: #28a428;
}

.btn-warning {
    background-color: var(--poe-orange);
    border-color: var(--poe-orange);
    color: white;
}

.btn-warning:hover {
    background-color: #e67e00;
    border-color: #e67e00;
    color: white;
}

/* Tables */
.table {
    background-color: white;
}

.table th {
    background-color: var(--poe-dark);
    color: white;
    border: none;
}

.table-hover tbody tr:hover {
    background-color: rgba(201, 170, 113, 0.1);
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border {
    color: var(--poe-gold);
}

/* Alerts */
.alert-info {
    background-color: rgba(65, 105, 225, 0.1);
    border-color: var(--poe-blue);
    color: var(--poe-blue);
}

.alert-success {
    background-color: rgba(50, 205, 50, 0.1);
    border-color: var(--poe-green);
    color: var(--poe-green);
}

.alert-warning {
    background-color: rgba(255, 140, 0, 0.1);
    border-color: var(--poe-orange);
    color: var(--poe-orange);
}

.alert-danger {
    background-color: rgba(220, 20, 60, 0.1);
    border-color: var(--poe-red);
    color: var(--poe-red);
}

/* Footer */
footer {
    background-color: var(--poe-darker) !important;
}

footer a {
    color: var(--poe-gold);
    text-decoration: none;
}

footer a:hover {
    color: #e6c589;
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .pattern-code {
        font-size: 0.8em;
    }
    
    .table-responsive {
        font-size: 0.9em;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--poe-gold);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #b8965f;
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Utility classes */
.text-poe-gold {
    color: var(--poe-gold);
}

.bg-poe-dark {
    background-color: var(--poe-dark);
}

.border-poe-gold {
    border-color: var(--poe-gold);
}

/* Form enhancements */
.form-control:focus {
    border-color: var(--poe-gold);
    box-shadow: 0 0 0 0.2rem rgba(201, 170, 113, 0.25);
}

.form-select:focus {
    border-color: var(--poe-gold);
    box-shadow: 0 0 0 0.2rem rgba(201, 170, 113, 0.25);
}
