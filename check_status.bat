@echo off
REM PoE 2 Tablet Optimizer - Status Check Script

echo ==========================================
echo  PoE 2 Tablet Optimizer - Status Check
echo ==========================================
echo.

REM Check Python installation
echo [1/6] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found or not in PATH
    set python_ok=0
) else (
    python --version
    echo ✓ Python is installed
    set python_ok=1
)
echo.

REM Check virtual environment
echo [2/6] Checking virtual environment...
if exist ".venv\Scripts\activate.bat" (
    echo ✓ Virtual environment found
    set venv_ok=1
) else (
    echo ❌ Virtual environment not found
    set venv_ok=0
)
echo.

REM Check if venv can be activated
if %venv_ok%==1 (
    echo [3/6] Testing virtual environment activation...
    call .venv\Scripts\activate.bat
    if errorlevel 1 (
        echo ❌ Failed to activate virtual environment
        set venv_active=0
    ) else (
        echo ✓ Virtual environment activated successfully
        set venv_active=1
    )
) else (
    echo [3/6] Skipping virtual environment test (not found)
    set venv_active=0
)
echo.

REM Check core dependencies
if %venv_active%==1 (
    echo [4/6] Checking core dependencies...
    python -c "import fastapi, uvicorn, pydantic_settings; print('✓ Core dependencies installed')" 2>nul
    if errorlevel 1 (
        echo ❌ Core dependencies missing
        set deps_ok=0
    ) else (
        echo ✓ Core dependencies found
        set deps_ok=1
    )
) else (
    echo [4/6] Skipping dependency check (virtual environment not active)
    set deps_ok=0
)
echo.

REM Check ML dependencies
if %venv_active%==1 (
    echo [5/6] Checking ML dependencies...
    python -c "import numpy, pandas, sklearn; print('✓ ML dependencies installed')" 2>nul
    if errorlevel 1 (
        echo ⚠️  ML dependencies missing (optional)
        set ml_deps_ok=0
    ) else (
        echo ✓ ML dependencies found
        set ml_deps_ok=1
    )
) else (
    echo [5/6] Skipping ML dependency check
    set ml_deps_ok=0
)
echo.

REM Check application
if %venv_active%==1 (
    echo [6/6] Testing application import...
    python -c "import app.main; print('✓ Application can be imported')" 2>nul
    if errorlevel 1 (
        echo ❌ Application import failed
        set app_ok=0
    ) else (
        echo ✓ Application ready
        set app_ok=1
    )
) else (
    echo [6/6] Skipping application test
    set app_ok=0
)
echo.

REM Summary
echo ==========================================
echo  Status Summary
echo ==========================================
if %python_ok%==1 echo ✓ Python installed
if %python_ok%==0 echo ❌ Python missing

if %venv_ok%==1 echo ✓ Virtual environment exists
if %venv_ok%==0 echo ❌ Virtual environment missing

if %venv_active%==1 echo ✓ Virtual environment works
if %venv_active%==0 echo ❌ Virtual environment issues

if %deps_ok%==1 echo ✓ Core dependencies installed
if %deps_ok%==0 echo ❌ Core dependencies missing

if %ml_deps_ok%==1 echo ✓ ML dependencies installed
if %ml_deps_ok%==0 echo ⚠️  ML dependencies missing

if %app_ok%==1 echo ✓ Application ready
if %app_ok%==0 echo ❌ Application not ready

echo.

REM Recommendations
if %python_ok%==0 (
    echo RECOMMENDATION: Install Python 3.9+ from https://python.org
)
if %venv_ok%==0 (
    echo RECOMMENDATION: Run setup.bat to create virtual environment
)
if %deps_ok%==0 (
    echo RECOMMENDATION: Run setup.bat to install dependencies
)
if %app_ok%==1 (
    echo.
    echo 🎉 Everything looks good! You can start the server with start_server.bat
)

echo.
pause
