"""
Pydantic schemas for regex pattern generation.
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class RegexPatternBase(BaseModel):
    """Base schema for regex patterns."""
    pattern: str = Field(..., description="The regex pattern string")
    description: str = Field(..., description="Human-readable description of what the pattern matches")
    value_tier: str = Field(..., description="Value tier this pattern targets")
    estimated_matches: int = Field(..., description="Estimated number of modifiers this pattern will match")
    complexity_score: float = Field(..., description="Pattern complexity (0-1, lower is simpler)")


class RegexPatternResponse(RegexPatternBase):
    """Schema for regex pattern API responses."""
    pattern_id: str = Field(..., description="Unique identifier for this pattern")
    tablet_type_id: int = Field(..., description="Tablet type this pattern is for")
    modifier_ids: List[int] = Field(..., description="Modifier IDs included in this pattern")
    confidence_score: float = Field(..., description="Confidence in pattern effectiveness")
    last_updated: datetime
    usage_count: int = Field(0, description="How many times this pattern has been used")
    success_rate: float = Field(0.0, description="Success rate when used")
    
    # Pattern metadata
    includes_trending: bool = Field(..., description="Whether pattern includes trending modifiers")
    includes_synergies: bool = Field(..., description="Whether pattern targets synergy combinations")
    optimization_level: str = Field(..., description="none, basic, or advanced")
    
    # Performance metrics
    precision: float = Field(..., description="Precision of pattern matching")
    recall: float = Field(..., description="Recall of pattern matching")
    f1_score: float = Field(..., description="F1 score of pattern performance")


class RegexGenerationRequest(BaseModel):
    """Schema for custom regex generation requests."""
    tablet_type_id: int = Field(..., description="Tablet type to generate patterns for")
    modifier_ids: Optional[List[int]] = Field(None, description="Specific modifiers to include")
    value_tiers: List[str] = Field(["high"], description="Value tiers to include")
    max_patterns: int = Field(3, le=10, description="Maximum number of patterns to generate")
    
    # Advanced options
    include_trending: bool = Field(True, description="Include trending modifiers")
    include_synergies: bool = Field(False, description="Focus on synergy combinations")
    min_confidence: float = Field(0.7, description="Minimum confidence threshold")
    optimization_goal: str = Field("balance", description="coverage, precision, or balance")
    
    # Pattern constraints
    max_complexity: float = Field(0.8, description="Maximum pattern complexity")
    min_estimated_matches: int = Field(1, description="Minimum estimated matches per pattern")
    max_estimated_matches: int = Field(50, description="Maximum estimated matches per pattern")
    
    # Market conditions
    consider_market_trends: bool = Field(True, description="Consider current market trends")
    trend_weight: float = Field(0.3, description="Weight given to trending factors")
    volatility_filter: bool = Field(False, description="Filter out highly volatile modifiers")


class PatternTestResult(BaseModel):
    """Schema for pattern testing results."""
    test_string: str = Field(..., description="String that was tested")
    matches: bool = Field(..., description="Whether the pattern matched")
    match_groups: List[str] = Field(..., description="Captured groups from the match")
    match_position: Optional[Dict[str, int]] = Field(None, description="Start and end positions of match")
    confidence: float = Field(..., description="Confidence in the match result")


class PatternOptimizationResult(BaseModel):
    """Schema for pattern optimization results."""
    original_patterns: List[RegexPatternResponse]
    optimized_patterns: List[RegexPatternResponse]
    optimization_summary: Dict[str, Any] = Field(..., description="Summary of optimization changes")
    performance_improvement: Dict[str, float] = Field(..., description="Performance metrics improvement")
    coverage_analysis: Dict[str, Any] = Field(..., description="Coverage before and after optimization")


class RegexUsageStatistics(BaseModel):
    """Schema for regex usage statistics."""
    total_patterns_generated: int
    total_patterns_used: int
    average_success_rate: float
    most_popular_patterns: List[Dict[str, Any]]
    least_popular_patterns: List[Dict[str, Any]]
    
    # Performance metrics
    average_precision: float
    average_recall: float
    average_f1_score: float
    
    # Usage trends
    patterns_created_last_7d: int
    patterns_used_last_7d: int
    success_rate_trend: str = Field(..., description="improving, stable, or declining")
    
    # Tablet type breakdown
    usage_by_tablet_type: Dict[str, Dict[str, Any]]
    
    # Value tier effectiveness
    effectiveness_by_tier: Dict[str, float]
    
    statistics_period_days: int
    last_updated: datetime


class ModifierRegexInfo(BaseModel):
    """Schema for modifier regex information."""
    modifier_id: int
    modifier_name: str
    regex_pattern: Optional[str]
    regex_priority: int
    pattern_complexity: float
    estimated_match_rate: float
    last_pattern_update: Optional[datetime]
    pattern_effectiveness: Optional[float]
    is_included_in_patterns: bool
    inclusion_frequency: float = Field(..., description="How often this modifier is included in patterns")


class PatternExportData(BaseModel):
    """Schema for pattern export data."""
    export_format: str = Field(..., description="text, json, or csv")
    tablet_type_name: str
    patterns: List[Dict[str, Any]]
    export_metadata: Dict[str, Any] = Field(..., description="Export metadata and settings")
    export_timestamp: datetime
    total_patterns: int
    estimated_total_coverage: float = Field(..., description="Estimated percentage of valuable modifiers covered")


class SynergyPattern(BaseModel):
    """Schema for synergy-based patterns."""
    modifier1_id: int
    modifier2_id: int
    modifier1_name: str
    modifier2_name: str
    synergy_bonus: float
    combined_pattern: str = Field(..., description="Pattern that matches both modifiers")
    synergy_confidence: float = Field(..., description="Confidence in synergy calculation")
    market_opportunity: float = Field(..., description="Current market opportunity score")
    rarity_score: float = Field(..., description="How rare this combination is")
    profit_potential: float = Field(..., description="Estimated profit potential")
