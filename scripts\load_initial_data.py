"""
Load initial data for the PoE 2 Tablet Optimizer.
This script populates the database with tablet types, modifiers, and sample data.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.database import SessionLocal, init_db
from app.models.tablet import TabletType
from app.models.modifier import Modifier, ModifierCategory, ModifierValue
from datetime import datetime


def load_tablet_types(db: Session):
    """Load tablet types into the database."""
    tablet_types_data = [
        {
            "name": "atlas_passive_keystone",
            "display_name": "Atlas Passive Keystone",
            "description": "Keystone passive skills for the Atlas passive tree",
            "base_cost": 0.05,
            "crafting_cost": 0.02
        },
        {
            "name": "atlas_passive_notable",
            "display_name": "Atlas Passive Notable",
            "description": "Notable passive skills for the Atlas passive tree",
            "base_cost": 0.03,
            "crafting_cost": 0.02
        },
        {
            "name": "atlas_passive_small",
            "display_name": "Atlas Passive Small",
            "description": "Small passive skills for the Atlas passive tree",
            "base_cost": 0.01,
            "crafting_cost": 0.02
        },
        {
            "name": "breach_tablet",
            "display_name": "Breach Tablet",
            "description": "Tablets that modify Breach encounters",
            "base_cost": 0.08,
            "crafting_cost": 0.03
        },
        {
            "name": "delirium_tablet",
            "display_name": "Delirium Tablet",
            "description": "Tablets that modify Delirium encounters",
            "base_cost": 0.12,
            "crafting_cost": 0.04
        },
        {
            "name": "expedition_tablet",
            "display_name": "Expedition Tablet",
            "description": "Tablets that modify Expedition encounters",
            "base_cost": 0.06,
            "crafting_cost": 0.03
        },
        {
            "name": "ritual_tablet",
            "display_name": "Ritual Tablet",
            "description": "Tablets that modify Ritual encounters",
            "base_cost": 0.07,
            "crafting_cost": 0.03
        }
    ]
    
    for tablet_data in tablet_types_data:
        # Check if tablet type already exists
        existing = db.query(TabletType).filter(TabletType.name == tablet_data["name"]).first()
        if not existing:
            tablet_type = TabletType(**tablet_data)
            db.add(tablet_type)
            print(f"Added tablet type: {tablet_data['display_name']}")
    
    db.commit()


def load_modifiers(db: Session):
    """Load modifiers for each tablet type."""
    
    # Get tablet types
    tablet_types = db.query(TabletType).all()
    tablet_type_map = {tt.name: tt.id for tt in tablet_types}
    
    # Sample modifiers for different tablet types
    modifiers_data = [
        # Atlas Passive Keystone modifiers
        {
            "tablet_type": "atlas_passive_keystone",
            "name": "eldritch_battery",
            "display_name": "Eldritch Battery",
            "description": "Energy Shield protects Mana instead of Life",
            "category": ModifierCategory.SPECIAL,
            "base_value": 1.2,
            "is_valuable": True,
            "regex_pattern": r"Eldritch Battery",
            "regex_priority": 10
        },
        {
            "tablet_type": "atlas_passive_keystone",
            "name": "ghost_reaver",
            "display_name": "Ghost Reaver",
            "description": "Life Leech applies to Energy Shield instead",
            "category": ModifierCategory.DEFENSE,
            "base_value": 0.9,
            "is_valuable": True,
            "regex_pattern": r"Ghost Reaver",
            "regex_priority": 8
        },
        
        # Atlas Passive Notable modifiers
        {
            "tablet_type": "atlas_passive_notable",
            "name": "master_of_arms",
            "display_name": "Master of Arms",
            "description": "Increased Attack Damage with Two Handed Weapons",
            "category": ModifierCategory.DAMAGE,
            "base_value": 0.6,
            "is_valuable": True,
            "regex_pattern": r"Master of Arms",
            "regex_priority": 7
        },
        {
            "tablet_type": "atlas_passive_notable",
            "name": "deep_thoughts",
            "display_name": "Deep Thoughts",
            "description": "Increased Maximum Mana",
            "category": ModifierCategory.RESOURCE,
            "base_value": 0.4,
            "is_valuable": False,
            "regex_pattern": r"Deep Thoughts",
            "regex_priority": 5
        },
        
        # Breach Tablet modifiers
        {
            "tablet_type": "breach_tablet",
            "name": "breach_monster_life",
            "display_name": "Breach Monsters have increased Life",
            "description": "Breach Monsters in Areas have 25% increased Life",
            "category": ModifierCategory.UTILITY,
            "base_value": 0.3,
            "is_valuable": False,
            "regex_pattern": r"Breach Monsters.*increased Life",
            "regex_priority": 3
        },
        {
            "tablet_type": "breach_tablet",
            "name": "breach_splinter_chance",
            "display_name": "Breach Splinters have increased drop chance",
            "description": "Breach Splinters in Areas have 50% increased drop chance",
            "category": ModifierCategory.UTILITY,
            "base_value": 0.8,
            "is_valuable": True,
            "regex_pattern": r"Breach Splinters.*increased.*chance",
            "regex_priority": 9
        },
        
        # Delirium Tablet modifiers
        {
            "tablet_type": "delirium_tablet",
            "name": "delirium_reward_type",
            "display_name": "Delirium Rewards are Currency",
            "description": "Delirium Encounters in Areas have Currency Rewards",
            "category": ModifierCategory.UTILITY,
            "base_value": 1.5,
            "is_valuable": True,
            "regex_pattern": r"Delirium.*Currency Rewards",
            "regex_priority": 10
        },
        {
            "tablet_type": "delirium_tablet",
            "name": "delirium_monster_damage",
            "display_name": "Delirium Monsters deal increased Damage",
            "description": "Delirium Monsters in Areas deal 30% increased Damage",
            "category": ModifierCategory.UTILITY,
            "base_value": 0.2,
            "is_valuable": False,
            "regex_pattern": r"Delirium Monsters.*increased Damage",
            "regex_priority": 2
        },
        
        # Expedition Tablet modifiers
        {
            "tablet_type": "expedition_tablet",
            "name": "expedition_logbook_chance",
            "display_name": "Expedition Logbooks have increased drop chance",
            "description": "Expedition Logbooks in Areas have 100% increased drop chance",
            "category": ModifierCategory.UTILITY,
            "base_value": 1.1,
            "is_valuable": True,
            "regex_pattern": r"Expedition Logbooks.*increased.*chance",
            "regex_priority": 9
        },
        {
            "tablet_type": "expedition_tablet",
            "name": "expedition_artifact_quantity",
            "display_name": "Expedition Artifacts have increased quantity",
            "description": "Expedition Artifacts in Areas have 40% increased quantity",
            "category": ModifierCategory.UTILITY,
            "base_value": 0.7,
            "is_valuable": True,
            "regex_pattern": r"Expedition Artifacts.*increased quantity",
            "regex_priority": 7
        },
        
        # Ritual Tablet modifiers
        {
            "tablet_type": "ritual_tablet",
            "name": "ritual_tribute_quantity",
            "display_name": "Ritual Altars give increased Tribute",
            "description": "Ritual Altars in Areas give 50% increased Tribute",
            "category": ModifierCategory.UTILITY,
            "base_value": 0.9,
            "is_valuable": True,
            "regex_pattern": r"Ritual Altars.*increased Tribute",
            "regex_priority": 8
        },
        {
            "tablet_type": "ritual_tablet",
            "name": "ritual_monster_life",
            "display_name": "Ritual Monsters have increased Life",
            "description": "Ritual Monsters in Areas have 35% increased Life",
            "category": ModifierCategory.UTILITY,
            "base_value": 0.25,
            "is_valuable": False,
            "regex_pattern": r"Ritual Monsters.*increased Life",
            "regex_priority": 3
        }
    ]
    
    for mod_data in modifiers_data:
        tablet_type_id = tablet_type_map.get(mod_data["tablet_type"])
        if not tablet_type_id:
            print(f"Warning: Tablet type '{mod_data['tablet_type']}' not found")
            continue
        
        # Check if modifier already exists
        existing = db.query(Modifier).filter(
            Modifier.name == mod_data["name"],
            Modifier.tablet_type_id == tablet_type_id
        ).first()
        
        if not existing:
            modifier = Modifier(
                tablet_type_id=tablet_type_id,
                name=mod_data["name"],
                display_name=mod_data["display_name"],
                description=mod_data["description"],
                category=mod_data["category"],
                base_value=mod_data["base_value"],
                is_valuable=mod_data["is_valuable"],
                regex_pattern=mod_data["regex_pattern"],
                regex_priority=mod_data["regex_priority"]
            )
            db.add(modifier)
            print(f"Added modifier: {mod_data['display_name']}")
    
    db.commit()


def load_sample_modifier_values(db: Session):
    """Load sample modifier values for testing."""
    modifiers = db.query(Modifier).all()
    
    for modifier in modifiers:
        # Check if modifier value already exists
        existing = db.query(ModifierValue).filter(
            ModifierValue.modifier_id == modifier.id
        ).first()
        
        if not existing:
            # Create sample modifier value
            modifier_value = ModifierValue(
                modifier_id=modifier.id,
                average_value=modifier.base_value,
                min_value=modifier.base_value * 0.8,
                max_value=modifier.base_value * 1.2,
                sample_size=10,
                volatility=0.1,
                confidence=0.8,
                trend_direction="stable",
                data_source="initial_load",
                collection_method="manual"
            )
            db.add(modifier_value)
    
    db.commit()
    print(f"Added sample values for {len(modifiers)} modifiers")


def main():
    """Main function to load all initial data."""
    print("Initializing database...")
    init_db()
    
    print("Loading initial data...")
    db = SessionLocal()
    
    try:
        print("\n1. Loading tablet types...")
        load_tablet_types(db)
        
        print("\n2. Loading modifiers...")
        load_modifiers(db)
        
        print("\n3. Loading sample modifier values...")
        load_sample_modifier_values(db)
        
        print("\nInitial data loading completed successfully!")
        
        # Print summary
        tablet_count = db.query(TabletType).count()
        modifier_count = db.query(Modifier).count()
        value_count = db.query(ModifierValue).count()
        
        print(f"\nSummary:")
        print(f"- Tablet types: {tablet_count}")
        print(f"- Modifiers: {modifier_count}")
        print(f"- Modifier values: {value_count}")
        
    except Exception as e:
        print(f"Error loading initial data: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
