{% extends "base.html" %}

{% block title %}Manual Tablet Calculator - PoE 2 Tablet Optimizer{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">🧮 Manual Tablet Calculator</h1>
                    <p class="text-muted">Detailed evaluation tool for specific tablet configurations</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="loadSavedCalculations()">
                        <i class="fas fa-history"></i> Load Saved
                    </button>
                    <button class="btn btn-outline-success" onclick="exportCalculation()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Calculator Form -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Configuration</h5>
                </div>
                <div class="card-body">
                    <form id="calculatorForm" class="needs-validation" novalidate>
                        <!-- Tablet Type Selection -->
                        <div class="mb-4">
                            <label for="tabletType" class="form-label">Tablet Type *</label>
                            <select class="form-select" id="tabletType" required>
                                <option value="">Select tablet type...</option>
                            </select>
                            <div class="invalid-feedback">Please select a tablet type.</div>
                        </div>

                        <!-- Modifier 1 Configuration -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Modifier 1</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="modifier1" class="form-label">Modifier</label>
                                        <select class="form-select" id="modifier1">
                                            <option value="">Select modifier...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="tier1" class="form-label">Tier</label>
                                        <select class="form-select" id="tier1">
                                            <option value="1">Tier 1</option>
                                            <option value="2">Tier 2</option>
                                            <option value="3">Tier 3</option>
                                            <option value="4">Tier 4</option>
                                            <option value="5">Tier 5</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <strong>Base Value:</strong> <span id="mod1BaseValue">-</span> ex
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <strong>Category:</strong> <span id="mod1Category">-</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Modifier 2 Configuration -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Modifier 2 (Optional)</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="modifier2" class="form-label">Modifier</label>
                                        <select class="form-select" id="modifier2">
                                            <option value="">Select modifier...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="tier2" class="form-label">Tier</label>
                                        <select class="form-select" id="tier2">
                                            <option value="1">Tier 1</option>
                                            <option value="2">Tier 2</option>
                                            <option value="3">Tier 3</option>
                                            <option value="4">Tier 4</option>
                                            <option value="5">Tier 5</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <strong>Base Value:</strong> <span id="mod2BaseValue">-</span> ex
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <strong>Category:</strong> <span id="mod2Category">-</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Options -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <button class="btn btn-link p-0 text-decoration-none" type="button" 
                                            data-bs-toggle="collapse" data-bs-target="#advancedOptions">
                                        <i class="fas fa-chevron-down"></i> Advanced Options
                                    </button>
                                </h6>
                            </div>
                            <div class="collapse" id="advancedOptions">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="marketCondition" class="form-label">Market Condition</label>
                                            <select class="form-select" id="marketCondition">
                                                <option value="normal">Normal</option>
                                                <option value="bullish">Bullish</option>
                                                <option value="bearish">Bearish</option>
                                                <option value="volatile">Volatile</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="timeHorizon" class="form-label">Time Horizon</label>
                                            <select class="form-select" id="timeHorizon">
                                                <option value="immediate">Immediate Sale</option>
                                                <option value="short">Short Term (1-7 days)</option>
                                                <option value="medium">Medium Term (1-4 weeks)</option>
                                                <option value="long">Long Term (1+ months)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="riskTolerance" class="form-label">Risk Tolerance</label>
                                            <select class="form-select" id="riskTolerance">
                                                <option value="conservative">Conservative</option>
                                                <option value="moderate">Moderate</option>
                                                <option value="aggressive">Aggressive</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="includePredictions">
                                                <label class="form-check-label" for="includePredictions">
                                                    Include ML Predictions
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-calculator"></i> Calculate Value
                            </button>
                            <div class="row">
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-secondary w-100" onclick="resetForm()">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-outline-info w-100" onclick="loadExample()">
                                        <i class="fas fa-lightbulb"></i> Load Example
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Results Panel -->
        <div class="col-lg-6">
            <!-- Quick Summary -->
            <div class="card mb-4" id="quickSummary" style="display: none;">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Quick Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h3 class="text-success" id="totalValue">0.00</h3>
                            <small>Total Value (ex)</small>
                        </div>
                        <div class="col-4">
                            <h3 class="text-info" id="profitEstimate">0.00</h3>
                            <small>Profit Estimate (ex)</small>
                        </div>
                        <div class="col-4">
                            <h3 id="recommendation">-</h3>
                            <small>Recommendation</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Results -->
            <div class="card" id="detailedResults" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list-alt"></i> Detailed Analysis</h5>
                </div>
                <div class="card-body">
                    <!-- Value Breakdown -->
                    <div class="mb-4">
                        <h6>Value Breakdown</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody id="valueBreakdown">
                                    <!-- Populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Synergy Analysis -->
                    <div class="mb-4" id="synergySection" style="display: none;">
                        <h6>Synergy Analysis</h6>
                        <div class="alert alert-info" id="synergyInfo">
                            <!-- Populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Risk Assessment -->
                    <div class="mb-4">
                        <h6>Risk Assessment</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between">
                                    <span>Risk Level:</span>
                                    <span id="riskLevel" class="badge">-</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between">
                                    <span>Confidence:</span>
                                    <span id="confidence">-</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted" id="riskFactors">
                                <!-- Populated by JavaScript -->
                            </small>
                        </div>
                    </div>

                    <!-- Market Context -->
                    <div class="mb-4">
                        <h6>Market Context</h6>
                        <div id="marketContext">
                            <!-- Populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="saveCalculation()">
                            <i class="fas fa-save"></i> Save Calculation
                        </button>
                        <div class="row">
                            <div class="col-6">
                                <button class="btn btn-outline-info w-100" onclick="compareWithSimilar()">
                                    <i class="fas fa-balance-scale"></i> Compare
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-success w-100" onclick="generateRegexForThis()">
                                    <i class="fas fa-search"></i> Generate Regex
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visualization -->
            <div class="card mt-4" id="visualizationCard" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Value Visualization</h5>
                </div>
                <div class="card-body">
                    <canvas id="valueChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Comparison Modal -->
    <div class="modal fade" id="comparisonModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Similar Tablet Comparison</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="comparisonContent">
                        <!-- Populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Saved Calculations Modal -->
    <div class="modal fade" id="savedCalculationsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Saved Calculations</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="savedCalculationsContent">
                        <!-- Populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-danger" onclick="clearSavedCalculations()">
                        <i class="fas fa-trash"></i> Clear All
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/calculator.js') }}"></script>
{% endblock %}
