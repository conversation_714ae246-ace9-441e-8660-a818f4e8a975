"""
Pydantic schemas for modifier-related API operations.
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models.modifier import ModifierCategory


class ModifierBase(BaseModel):
    """Base schema for modifiers."""
    name: str = Field(..., description="Internal name of the modifier")
    display_name: str = Field(..., description="Display name for users")
    description: Optional[str] = Field(None, description="Description of the modifier")
    category: ModifierCategory = Field(ModifierCategory.UTILITY, description="Modifier category")
    min_tier: int = Field(1, description="Minimum tier for this modifier")
    max_tier: int = Field(1, description="Maximum tier for this modifier")
    weight: int = Field(100, description="Spawn weight in game")
    base_value: float = Field(0.0, description="Base value in exalted orbs")
    regex_pattern: Optional[str] = Field(None, description="Regex pattern for inventory search")
    regex_priority: int = Field(0, description="Priority for pattern generation")


class ModifierCreate(ModifierBase):
    """Schema for creating a new modifier."""
    tablet_type_id: int = Field(..., description="ID of the tablet type this modifier belongs to")


class ModifierResponse(ModifierBase):
    """Schema for modifier API responses."""
    id: int
    tablet_type_id: int
    is_valuable: bool = Field(..., description="Whether this modifier is considered valuable")
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    # Computed properties
    current_value: float = Field(..., description="Most recent market value")
    value_tier: str = Field(..., description="Value tier: high, medium, or low")
    
    class Config:
        from_attributes = True


class ModifierTierBase(BaseModel):
    """Base schema for modifier tiers."""
    tier: int = Field(..., description="Tier number (1 = lowest)")
    min_value: Optional[float] = Field(None, description="Minimum roll value")
    max_value: Optional[float] = Field(None, description="Maximum roll value")
    display_text: Optional[str] = Field(None, description="How it appears in game")
    value_multiplier: float = Field(1.0, description="Market value multiplier for this tier")


class ModifierTierCreate(ModifierTierBase):
    """Schema for creating a new modifier tier."""
    modifier_id: int = Field(..., description="ID of the modifier this tier belongs to")


class ModifierTierResponse(ModifierTierBase):
    """Schema for modifier tier API responses."""
    id: int
    modifier_id: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class ModifierValueBase(BaseModel):
    """Base schema for modifier values."""
    average_value: float = Field(..., description="Average market value")
    min_value: Optional[float] = Field(None, description="Minimum observed value")
    max_value: Optional[float] = Field(None, description="Maximum observed value")
    sample_size: int = Field(1, description="Number of data points")
    volatility: float = Field(0.0, description="Price volatility (0-1)")
    confidence: float = Field(0.0, description="Confidence in data (0-1)")
    trend_direction: Optional[str] = Field(None, description="up, down, or stable")
    data_source: Optional[str] = Field(None, description="Where the data came from")
    collection_method: Optional[str] = Field(None, description="How it was collected")


class ModifierValueCreate(ModifierValueBase):
    """Schema for creating a new modifier value."""
    modifier_id: int = Field(..., description="ID of the modifier")


class ModifierValueResponse(ModifierValueBase):
    """Schema for modifier value API responses."""
    id: int
    modifier_id: int
    valid_from: datetime
    valid_to: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True


class ModifierSynergy(BaseModel):
    """Schema for modifier synergy information."""
    modifier1_id: int
    modifier2_id: int
    bonus_value: float = Field(..., description="Additional value from synergy")
    synergy_type: str = Field(..., description="Type of synergy")
    description: str = Field(..., description="Description of the synergy")
    confidence: float = Field(..., description="Confidence in synergy calculation")


class ModifierCategorySummary(BaseModel):
    """Schema for modifier category summary statistics."""
    category: ModifierCategory
    total_modifiers: int
    valuable_modifiers: int
    average_value: float
    highest_value: float
    lowest_value: float
    trending_up_count: int
    trending_down_count: int


class TrendingModifier(BaseModel):
    """Schema for trending modifier information."""
    modifier: ModifierResponse
    trend_direction: str = Field(..., description="up or down")
    trend_strength: float = Field(..., description="Strength of trend (0-1)")
    price_change_percent: float = Field(..., description="Percentage price change")
    volume_change_percent: float = Field(..., description="Percentage volume change")
    days_trending: int = Field(..., description="Number of days trend has been active")
    prediction_confidence: float = Field(..., description="Confidence in trend continuation")


class ModifierSearchResult(BaseModel):
    """Schema for modifier search results."""
    modifiers: List[ModifierResponse]
    total_count: int
    search_query: Optional[str]
    filters_applied: Dict[str, Any]
    execution_time_ms: int
