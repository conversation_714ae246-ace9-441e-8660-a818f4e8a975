#!/usr/bin/env python3
"""
Test runner script for PoE 2 Tablet Optimizer.
Provides various testing options and configurations.
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, description=""):
    """Run a command and handle errors."""
    if description:
        print(f"\n🔄 {description}")
    
    print(f"Running: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"✅ {description or 'Command'} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Command'} failed")
        print(f"Error: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False


def install_test_dependencies():
    """Install test dependencies."""
    dependencies = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.10.0",
        "httpx>=0.24.0",
        "factory-boy>=3.2.0",
        "faker>=18.0.0"
    ]
    
    for dep in dependencies:
        if not run_command(["pip", "install", dep], f"Installing {dep}"):
            return False
    
    return True


def run_unit_tests():
    """Run unit tests only."""
    command = [
        "python", "-m", "pytest",
        "tests/",
        "-m", "unit",
        "-v",
        "--tb=short"
    ]
    return run_command(command, "Running unit tests")


def run_integration_tests():
    """Run integration tests only."""
    command = [
        "python", "-m", "pytest",
        "tests/",
        "-m", "integration",
        "-v",
        "--tb=short"
    ]
    return run_command(command, "Running integration tests")


def run_api_tests():
    """Run API tests only."""
    command = [
        "python", "-m", "pytest",
        "tests/test_api.py",
        "-v",
        "--tb=short"
    ]
    return run_command(command, "Running API tests")


def run_service_tests():
    """Run service layer tests only."""
    command = [
        "python", "-m", "pytest",
        "tests/test_services.py",
        "-v",
        "--tb=short"
    ]
    return run_command(command, "Running service tests")


def run_all_tests():
    """Run all tests with coverage."""
    command = [
        "python", "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--cov=app",
        "--cov-report=html",
        "--cov-report=term-missing",
        "--cov-fail-under=70"
    ]
    return run_command(command, "Running all tests with coverage")


def run_performance_tests():
    """Run performance tests."""
    command = [
        "python", "-m", "pytest",
        "tests/",
        "-m", "performance",
        "-v",
        "--tb=short"
    ]
    return run_command(command, "Running performance tests")


def run_security_tests():
    """Run security tests."""
    command = [
        "python", "-m", "pytest",
        "tests/",
        "-m", "security",
        "-v",
        "--tb=short"
    ]
    return run_command(command, "Running security tests")


def run_quick_tests():
    """Run quick tests (excluding slow ones)."""
    command = [
        "python", "-m", "pytest",
        "tests/",
        "-m", "not slow",
        "-v",
        "--tb=short"
    ]
    return run_command(command, "Running quick tests")


def run_linting():
    """Run code linting."""
    print("\n🔍 Running code quality checks...")
    
    # Install linting tools if needed
    linting_tools = ["flake8", "black", "isort", "mypy"]
    for tool in linting_tools:
        subprocess.run(["pip", "install", tool], capture_output=True)
    
    success = True
    
    # Run black (code formatting)
    if not run_command(["black", "--check", "app/", "tests/"], "Checking code formatting with black"):
        print("💡 Run 'black app/ tests/' to fix formatting issues")
        success = False
    
    # Run isort (import sorting)
    if not run_command(["isort", "--check-only", "app/", "tests/"], "Checking import sorting with isort"):
        print("💡 Run 'isort app/ tests/' to fix import sorting")
        success = False
    
    # Run flake8 (style guide)
    if not run_command(["flake8", "app/", "tests/"], "Checking style guide with flake8"):
        success = False
    
    # Run mypy (type checking)
    if not run_command(["mypy", "app/"], "Running type checking with mypy"):
        success = False
    
    return success


def setup_test_environment():
    """Set up test environment."""
    print("🔧 Setting up test environment...")
    
    # Set environment variables for testing
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = "sqlite:///./test.db"
    
    # Create test directories
    test_dirs = ["tests/reports", "tests/coverage"]
    for dir_path in test_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ Test environment setup complete")


def cleanup_test_environment():
    """Clean up test environment."""
    print("🧹 Cleaning up test environment...")
    
    # Remove test database
    test_db_path = Path("test.db")
    if test_db_path.exists():
        test_db_path.unlink()
    
    # Remove __pycache__ directories
    for pycache in Path(".").rglob("__pycache__"):
        if pycache.is_dir():
            import shutil
            shutil.rmtree(pycache)
    
    print("✅ Test environment cleanup complete")


def generate_test_report():
    """Generate comprehensive test report."""
    print("\n📊 Generating test report...")
    
    # Run tests with detailed reporting
    command = [
        "python", "-m", "pytest",
        "tests/",
        "--tb=short",
        "--cov=app",
        "--cov-report=html:tests/reports/coverage",
        "--cov-report=xml:tests/reports/coverage.xml",
        "--junit-xml=tests/reports/junit.xml",
        "-v"
    ]
    
    success = run_command(command, "Generating test report")
    
    if success:
        print("\n📋 Test Report Generated:")
        print("  - HTML Coverage: tests/reports/coverage/index.html")
        print("  - XML Coverage: tests/reports/coverage.xml")
        print("  - JUnit XML: tests/reports/junit.xml")
    
    return success


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="PoE 2 Tablet Optimizer Test Runner")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--api", action="store_true", help="Run API tests only")
    parser.add_argument("--services", action="store_true", help="Run service tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--security", action="store_true", help="Run security tests")
    parser.add_argument("--quick", action="store_true", help="Run quick tests (exclude slow)")
    parser.add_argument("--lint", action="store_true", help="Run code linting")
    parser.add_argument("--report", action="store_true", help="Generate comprehensive test report")
    parser.add_argument("--cleanup", action="store_true", help="Clean up test environment")
    parser.add_argument("--all", action="store_true", help="Run all tests with coverage")
    
    args = parser.parse_args()
    
    # Setup test environment
    setup_test_environment()
    
    success = True
    
    try:
        if args.install_deps:
            success &= install_test_dependencies()
        
        if args.lint:
            success &= run_linting()
        
        if args.unit:
            success &= run_unit_tests()
        elif args.integration:
            success &= run_integration_tests()
        elif args.api:
            success &= run_api_tests()
        elif args.services:
            success &= run_service_tests()
        elif args.performance:
            success &= run_performance_tests()
        elif args.security:
            success &= run_security_tests()
        elif args.quick:
            success &= run_quick_tests()
        elif args.report:
            success &= generate_test_report()
        elif args.all:
            success &= run_all_tests()
        else:
            # Default: run quick tests
            print("No specific test type specified. Running quick tests...")
            success &= run_quick_tests()
        
        if args.cleanup:
            cleanup_test_environment()
        
        if success:
            print("\n🎉 All tests completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        cleanup_test_environment()
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        cleanup_test_environment()
        sys.exit(1)


if __name__ == "__main__":
    main()
