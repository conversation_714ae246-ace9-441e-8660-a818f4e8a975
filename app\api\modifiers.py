"""
API endpoints for modifier operations.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.models.modifier import Modifier, ModifierCategory
from app.schemas.modifier import ModifierResponse, ModifierValueResponse
from app.services.modifier_service import ModifierService

router = APIRouter()


@router.get("/")
async def get_modifiers(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    category: Optional[ModifierCategory] = Query(None, description="Filter by category"),
    is_valuable: Optional[bool] = Query(None, description="Filter by value tier"),
    min_value: Optional[float] = Query(None, description="Minimum current value"),
    search: Optional[str] = Query(None, description="Search in name or description"),
    limit: int = Query(100, le=500, description="Maximum number of results"),
    offset: int = Query(0, description="Number of results to skip"),
    db: Session = Depends(get_db)
):
    """Get modifiers with optional filtering and search."""
    query = db.query(Modifier).filter(Modifier.is_active == True)
    
    if tablet_type_id:
        query = query.filter(Modifier.tablet_type_id == tablet_type_id)
    
    if category:
        query = query.filter(Modifier.category == category)
    
    if is_valuable is not None:
        query = query.filter(Modifier.is_valuable == is_valuable)
    
    if min_value is not None:
        query = query.filter(Modifier.base_value >= min_value)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            Modifier.name.ilike(search_term) | 
            Modifier.display_name.ilike(search_term) |
            Modifier.description.ilike(search_term)
        )
    
    modifiers = query.offset(offset).limit(limit).all()

    # Convert to response format with computed properties
    response_modifiers = []
    for modifier in modifiers:
        modifier_dict = {
            "id": modifier.id,
            "tablet_type_id": modifier.tablet_type_id,
            "name": modifier.name,
            "display_name": modifier.display_name,
            "description": modifier.description,
            "category": modifier.category,
            "min_tier": modifier.min_tier,
            "max_tier": modifier.max_tier,
            "weight": modifier.weight,
            "base_value": modifier.base_value,
            "regex_pattern": modifier.regex_pattern,
            "regex_priority": modifier.regex_priority,
            "is_valuable": modifier.is_valuable,
            "is_active": modifier.is_active,
            "created_at": modifier.created_at,
            "updated_at": modifier.updated_at,
            "current_value": modifier.base_value or 0.0,  # Use base_value as current_value for now
            "value_tier": "high" if modifier.base_value and modifier.base_value >= 0.8 else "medium" if modifier.base_value and modifier.base_value >= 0.4 else "low"
        }
        response_modifiers.append(modifier_dict)

    return response_modifiers


@router.get("/{modifier_id}", response_model=ModifierResponse)
async def get_modifier(
    modifier_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific modifier by ID."""
    modifier = db.query(Modifier).filter(Modifier.id == modifier_id).first()
    if not modifier:
        raise HTTPException(status_code=404, detail="Modifier not found")
    return modifier


@router.get("/{modifier_id}/values", response_model=List[ModifierValueResponse])
async def get_modifier_values(
    modifier_id: int,
    days: int = Query(30, description="Number of days of history to return"),
    db: Session = Depends(get_db)
):
    """Get value history for a modifier."""
    modifier_service = ModifierService(db)
    values = await modifier_service.get_value_history(modifier_id, days)
    return values


@router.get("/valuable/by-tablet-type/{tablet_type_id}")
async def get_valuable_modifiers_for_tablet(
    tablet_type_id: int,
    value_tier: str = Query("high", regex="^(high|medium|low)$"),
    db: Session = Depends(get_db)
):
    """Get valuable modifiers for a specific tablet type."""
    modifier_service = ModifierService(db)
    modifiers = await modifier_service.get_valuable_modifiers(tablet_type_id, value_tier)
    return modifiers


@router.get("/synergies/{modifier1_id}/{modifier2_id}")
async def get_modifier_synergy(
    modifier1_id: int,
    modifier2_id: int,
    db: Session = Depends(get_db)
):
    """Calculate synergy bonus between two modifiers."""
    modifier_service = ModifierService(db)
    synergy = await modifier_service.calculate_synergy(modifier1_id, modifier2_id)
    return {
        "modifier1_id": modifier1_id,
        "modifier2_id": modifier2_id,
        "synergy_bonus": synergy.bonus_value,
        "synergy_type": synergy.synergy_type,
        "description": synergy.description
    }


@router.get("/categories/summary")
async def get_category_summary(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    db: Session = Depends(get_db)
):
    """Get summary statistics by modifier category."""
    modifier_service = ModifierService(db)
    summary = await modifier_service.get_category_summary(tablet_type_id)
    return summary


@router.get("/trending/up")
async def get_trending_up_modifiers(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    days: int = Query(7, description="Trend analysis period"),
    limit: int = Query(20, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """Get modifiers that are trending upward in value."""
    modifier_service = ModifierService(db)
    trending = await modifier_service.get_trending_modifiers("up", tablet_type_id, days, limit)
    return trending


@router.get("/trending/down")
async def get_trending_down_modifiers(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    days: int = Query(7, description="Trend analysis period"),
    limit: int = Query(20, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """Get modifiers that are trending downward in value."""
    modifier_service = ModifierService(db)
    trending = await modifier_service.get_trending_modifiers("down", tablet_type_id, days, limit)
    return trending
