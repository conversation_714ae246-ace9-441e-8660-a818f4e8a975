"""
Service layer for modifier operations.
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.models.modifier import Modifier, ModifierValue, ModifierCategory
from app.models.market import MarketData
from app.schemas.modifier import ModifierCategorySummary, TrendingModifier, ModifierSynergy
from app.config import data_config


class ModifierService:
    """Service for modifier-related business logic."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_value_history(self, modifier_id: int, days: int = 30) -> List[ModifierValue]:
        """Get value history for a modifier."""
        since_date = datetime.utcnow() - timedelta(days=days)
        
        values = self.db.query(ModifierValue).filter(
            and_(
                ModifierValue.modifier_id == modifier_id,
                ModifierValue.created_at >= since_date
            )
        ).order_by(ModifierValue.created_at.desc()).all()
        
        return values
    
    async def get_valuable_modifiers(self, tablet_type_id: int, value_tier: str = "high") -> List[Modifier]:
        """Get valuable modifiers for a specific tablet type."""
        query = self.db.query(Modifier).filter(
            and_(
                Modifier.tablet_type_id == tablet_type_id,
                Modifier.is_active == True
            )
        )
        
        # Filter by value tier
        if value_tier == "high":
            query = query.filter(Modifier.base_value >= data_config.HIGH_VALUE_THRESHOLD)
        elif value_tier == "medium":
            query = query.filter(
                and_(
                    Modifier.base_value >= data_config.MEDIUM_VALUE_THRESHOLD,
                    Modifier.base_value < data_config.HIGH_VALUE_THRESHOLD
                )
            )
        elif value_tier == "low":
            query = query.filter(Modifier.base_value < data_config.MEDIUM_VALUE_THRESHOLD)
        
        # Order by value descending
        modifiers = query.order_by(desc(Modifier.base_value)).all()
        return modifiers
    
    async def calculate_synergy(self, modifier1_id: int, modifier2_id: int) -> ModifierSynergy:
        """Calculate synergy bonus between two modifiers."""
        modifier1 = self.db.query(Modifier).filter(Modifier.id == modifier1_id).first()
        modifier2 = self.db.query(Modifier).filter(Modifier.id == modifier2_id).first()
        
        if not modifier1 or not modifier2:
            raise ValueError("One or both modifiers not found")
        
        if modifier1.tablet_type_id != modifier2.tablet_type_id:
            raise ValueError("Modifiers must be from the same tablet type")
        
        # Calculate synergy bonus
        bonus_value = 0.0
        synergy_type = "none"
        description = "No synergy detected"
        confidence = 0.5
        
        # Same category synergy
        if modifier1.category == modifier2.category:
            bonus_value = (modifier1.current_value + modifier2.current_value) * 0.1
            synergy_type = "same_category"
            description = f"Same category bonus ({modifier1.category.value})"
            confidence = 0.8
        
        # Complementary synergies
        elif self._are_complementary(modifier1.category, modifier2.category):
            multiplier = self._get_complementary_multiplier(modifier1.category, modifier2.category)
            bonus_value = (modifier1.current_value + modifier2.current_value) * multiplier
            synergy_type = "complementary"
            description = f"Complementary synergy ({modifier1.category.value} + {modifier2.category.value})"
            confidence = 0.7
        
        # Anti-synergy (negative synergy)
        elif self._are_anti_synergistic(modifier1.category, modifier2.category):
            bonus_value = -((modifier1.current_value + modifier2.current_value) * 0.05)
            synergy_type = "anti_synergy"
            description = f"Anti-synergy penalty ({modifier1.category.value} + {modifier2.category.value})"
            confidence = 0.6
        
        return ModifierSynergy(
            modifier1_id=modifier1_id,
            modifier2_id=modifier2_id,
            bonus_value=bonus_value,
            synergy_type=synergy_type,
            description=description,
            confidence=confidence
        )
    
    def _are_complementary(self, cat1: ModifierCategory, cat2: ModifierCategory) -> bool:
        """Check if two modifier categories are complementary."""
        complementary_pairs = {
            (ModifierCategory.DAMAGE, ModifierCategory.UTILITY),
            (ModifierCategory.DEFENSE, ModifierCategory.RESOURCE),
            (ModifierCategory.DAMAGE, ModifierCategory.RESOURCE),
            (ModifierCategory.UTILITY, ModifierCategory.SPECIAL),
        }
        
        return (cat1, cat2) in complementary_pairs or (cat2, cat1) in complementary_pairs
    
    def _get_complementary_multiplier(self, cat1: ModifierCategory, cat2: ModifierCategory) -> float:
        """Get the synergy multiplier for complementary categories."""
        multipliers = {
            (ModifierCategory.DAMAGE, ModifierCategory.UTILITY): 0.15,
            (ModifierCategory.DEFENSE, ModifierCategory.RESOURCE): 0.12,
            (ModifierCategory.DAMAGE, ModifierCategory.RESOURCE): 0.08,
            (ModifierCategory.UTILITY, ModifierCategory.SPECIAL): 0.10,
        }
        
        return multipliers.get((cat1, cat2), multipliers.get((cat2, cat1), 0.05))
    
    def _are_anti_synergistic(self, cat1: ModifierCategory, cat2: ModifierCategory) -> bool:
        """Check if two modifier categories have anti-synergy."""
        # Example: damage and defense might not synergize well
        anti_pairs = {
            (ModifierCategory.DAMAGE, ModifierCategory.DEFENSE),
        }
        
        return (cat1, cat2) in anti_pairs or (cat2, cat1) in anti_pairs
    
    async def get_category_summary(self, tablet_type_id: Optional[int] = None) -> List[ModifierCategorySummary]:
        """Get summary statistics by modifier category."""
        query = self.db.query(Modifier).filter(Modifier.is_active == True)
        
        if tablet_type_id:
            query = query.filter(Modifier.tablet_type_id == tablet_type_id)
        
        modifiers = query.all()
        
        # Group by category
        category_data = {}
        for modifier in modifiers:
            category = modifier.category
            if category not in category_data:
                category_data[category] = []
            category_data[category].append(modifier)
        
        summaries = []
        for category, mods in category_data.items():
            valuable_count = sum(1 for m in mods if m.is_valuable)
            values = [m.current_value for m in mods]
            
            # Get trending counts (simplified)
            trending_up = sum(1 for m in mods if self._is_trending_up(m))
            trending_down = sum(1 for m in mods if self._is_trending_down(m))
            
            summary = ModifierCategorySummary(
                category=category,
                total_modifiers=len(mods),
                valuable_modifiers=valuable_count,
                average_value=sum(values) / len(values) if values else 0.0,
                highest_value=max(values) if values else 0.0,
                lowest_value=min(values) if values else 0.0,
                trending_up_count=trending_up,
                trending_down_count=trending_down
            )
            summaries.append(summary)
        
        return summaries
    
    def _is_trending_up(self, modifier: Modifier) -> bool:
        """Check if a modifier is trending upward (simplified)."""
        # In a real implementation, this would check market data trends
        return modifier.current_value > modifier.base_value * 1.1
    
    def _is_trending_down(self, modifier: Modifier) -> bool:
        """Check if a modifier is trending downward (simplified)."""
        # In a real implementation, this would check market data trends
        return modifier.current_value < modifier.base_value * 0.9
    
    async def get_trending_modifiers(self, direction: str, tablet_type_id: Optional[int] = None, 
                                   days: int = 7, limit: int = 20) -> List[TrendingModifier]:
        """Get modifiers that are trending in a specific direction."""
        query = self.db.query(Modifier).filter(Modifier.is_active == True)
        
        if tablet_type_id:
            query = query.filter(Modifier.tablet_type_id == tablet_type_id)
        
        modifiers = query.all()
        trending = []
        
        for modifier in modifiers:
            # Calculate trend metrics (simplified)
            price_change = self._calculate_price_change(modifier, days)
            volume_change = self._calculate_volume_change(modifier, days)
            trend_strength = abs(price_change) / 100.0  # Convert percentage to 0-1
            
            # Filter by direction
            if direction == "up" and price_change <= 0:
                continue
            elif direction == "down" and price_change >= 0:
                continue
            
            trending_modifier = TrendingModifier(
                modifier=modifier,
                trend_direction=direction,
                trend_strength=min(trend_strength, 1.0),
                price_change_percent=price_change,
                volume_change_percent=volume_change,
                days_trending=days,  # Simplified
                prediction_confidence=0.7  # Simplified
            )
            trending.append(trending_modifier)
        
        # Sort by trend strength and limit results
        trending.sort(key=lambda x: x.trend_strength, reverse=True)
        return trending[:limit]
    
    def _calculate_price_change(self, modifier: Modifier, days: int) -> float:
        """Calculate price change percentage over the specified period."""
        # Simplified calculation - in reality would use historical data
        base_change = (modifier.current_value - modifier.base_value) / modifier.base_value * 100
        return base_change
    
    def _calculate_volume_change(self, modifier: Modifier, days: int) -> float:
        """Calculate volume change percentage over the specified period."""
        # Simplified calculation - would use actual market data
        return 0.0  # Placeholder
