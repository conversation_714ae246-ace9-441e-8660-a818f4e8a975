"""
Regex pattern generation service for PoE 2 inventory search optimization.
Generates intelligent search patterns for finding valuable tablets in inventory.
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from typing import Dict, List, Optional, Tuple, Any
import re
import logging

from app.models.modifier import Modifier, ModifierCategory
from app.models.tablet import TabletType
from app.schemas.regex_generator import RegexPattern, SearchStrategy, PatternOptimization
from app.services.valuation_service import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.config import data_config

logger = logging.getLogger(__name__)


class RegexService:
    """Advanced regex pattern generator for PoE 2 inventory search."""
    
    def __init__(self, db: Session):
        self.db = db
        self.valuation_engine = ValuationEngine(db)
        
        # Pattern optimization settings
        self.max_pattern_length = 100
        self.min_confidence_threshold = 0.6
        self.value_tier_thresholds = {
            "high": data_config.HIGH_VALUE_THRESHOLD,
            "medium": data_config.MEDIUM_VALUE_THRESHOLD,
            "low": 0.0
        }
    
    async def generate_optimal_pattern(self, tablet_type_id: Optional[int] = None,
                                     value_tier: str = "high",
                                     max_patterns: int = 5,
                                     include_synergies: bool = True) -> List[RegexPattern]:
        """Generate optimal regex patterns for finding valuable tablets."""
        try:
            # Get valuable modifiers
            valuable_modifiers = await self._get_valuable_modifiers(tablet_type_id, value_tier)
            
            if not valuable_modifiers:
                return []
            
            # Generate individual patterns
            individual_patterns = await self._generate_individual_patterns(valuable_modifiers)
            
            # Generate combination patterns if synergies are enabled
            combination_patterns = []
            if include_synergies:
                combination_patterns = await self._generate_combination_patterns(valuable_modifiers)
            
            # Combine and optimize patterns
            all_patterns = individual_patterns + combination_patterns
            optimized_patterns = await self._optimize_patterns(all_patterns, max_patterns)
            
            return optimized_patterns
            
        except Exception as e:
            logger.error(f"Error generating optimal patterns: {e}")
            return []
    
    async def generate_search_strategy(self, tablet_type_id: Optional[int] = None,
                                     time_budget_seconds: int = 30,
                                     inventory_size: int = 60) -> SearchStrategy:
        """Generate comprehensive search strategy for inventory scanning."""
        try:
            # Generate patterns for different value tiers
            high_value_patterns = await self.generate_optimal_pattern(
                tablet_type_id, "high", max_patterns=3
            )
            medium_value_patterns = await self.generate_optimal_pattern(
                tablet_type_id, "medium", max_patterns=2
            )
            
            # Calculate time allocation
            time_per_pattern = time_budget_seconds / max(len(high_value_patterns) + len(medium_value_patterns), 1)
            
            # Create search phases
            search_phases = []
            
            # Phase 1: High-value quick scan
            if high_value_patterns:
                search_phases.append({
                    "phase_name": "High-Value Quick Scan",
                    "patterns": high_value_patterns[:2],  # Top 2 patterns
                    "time_allocation": min(time_budget_seconds * 0.4, 15),
                    "priority": "critical",
                    "description": "Scan for highest value modifiers first"
                })
            
            # Phase 2: Comprehensive high-value scan
            if len(high_value_patterns) > 2:
                search_phases.append({
                    "phase_name": "Comprehensive High-Value Scan",
                    "patterns": high_value_patterns[2:],
                    "time_allocation": min(time_budget_seconds * 0.3, 10),
                    "priority": "high",
                    "description": "Thorough scan for all high-value modifiers"
                })
            
            # Phase 3: Medium-value scan
            if medium_value_patterns:
                search_phases.append({
                    "phase_name": "Medium-Value Scan",
                    "patterns": medium_value_patterns,
                    "time_allocation": time_budget_seconds * 0.3,
                    "priority": "medium",
                    "description": "Scan for medium-value opportunities"
                })
            
            # Calculate efficiency metrics
            total_coverage = await self._calculate_coverage(high_value_patterns + medium_value_patterns)
            expected_finds = await self._estimate_expected_finds(high_value_patterns + medium_value_patterns, inventory_size)
            
            return SearchStrategy(
                tablet_type_id=tablet_type_id,
                total_time_budget=time_budget_seconds,
                search_phases=search_phases,
                fallback_patterns=await self._generate_fallback_patterns(tablet_type_id),
                efficiency_score=total_coverage,
                expected_valuable_finds=expected_finds,
                optimization_notes=await self._generate_optimization_notes(search_phases)
            )
            
        except Exception as e:
            logger.error(f"Error generating search strategy: {e}")
            return SearchStrategy(
                tablet_type_id=tablet_type_id,
                total_time_budget=time_budget_seconds,
                search_phases=[],
                fallback_patterns=[],
                efficiency_score=0.0,
                expected_valuable_finds=0,
                optimization_notes=["Strategy generation failed"]
            )
    
    async def _get_valuable_modifiers(self, tablet_type_id: Optional[int], value_tier: str) -> List[Modifier]:
        """Get valuable modifiers based on criteria."""
        query = self.db.query(Modifier).filter(Modifier.is_active == True)
        
        if tablet_type_id:
            query = query.filter(Modifier.tablet_type_id == tablet_type_id)
        
        # Filter by value tier
        min_value = self.value_tier_thresholds[value_tier]
        query = query.filter(Modifier.base_value >= min_value)
        
        # Order by value and regex priority
        modifiers = query.order_by(
            desc(Modifier.base_value),
            desc(Modifier.regex_priority)
        ).all()
        
        return modifiers
    
    async def _generate_individual_patterns(self, modifiers: List[Modifier]) -> List[RegexPattern]:
        """Generate individual regex patterns for each modifier."""
        patterns = []
        
        for modifier in modifiers:
            if not modifier.regex_pattern:
                continue
            
            # Calculate pattern metrics
            specificity = await self._calculate_pattern_specificity(modifier.regex_pattern)
            coverage = await self._calculate_pattern_coverage(modifier)
            
            pattern = RegexPattern(
                pattern=modifier.regex_pattern,
                description=f"Search for {modifier.display_name}",
                target_modifiers=[modifier.id],
                expected_value=modifier.current_value,
                confidence=modifier.market_confidence if hasattr(modifier, 'market_confidence') else 0.8,
                specificity=specificity,
                coverage=coverage,
                priority=modifier.regex_priority,
                pattern_type="individual",
                optimization_level="standard"
            )
            
            patterns.append(pattern)
        
        return patterns
    
    async def _generate_combination_patterns(self, modifiers: List[Modifier]) -> List[RegexPattern]:
        """Generate combination patterns for synergistic modifiers."""
        patterns = []
        
        # Group modifiers by category for potential synergies
        category_groups = {}
        for modifier in modifiers:
            category = modifier.category
            if category not in category_groups:
                category_groups[category] = []
            category_groups[category].append(modifier)
        
        # Generate patterns for high-synergy combinations
        for i, mod1 in enumerate(modifiers[:10]):  # Limit to top 10 for performance
            for mod2 in modifiers[i+1:15]:  # Check combinations with next 5
                if mod1.tablet_type_id != mod2.tablet_type_id:
                    continue
                
                # Calculate synergy
                synergy = self.valuation_engine.synergy_calculator.calculate_synergy(mod1, mod2)
                
                if synergy["bonus_value"] > 0.1:  # Significant synergy
                    # Create OR pattern for either modifier
                    or_pattern = f"({mod1.regex_pattern}|{mod2.regex_pattern})"
                    
                    # Create AND pattern for both modifiers (more specific)
                    and_pattern = f"(?=.*{mod1.regex_pattern})(?=.*{mod2.regex_pattern})"
                    
                    # Add OR pattern (broader search)
                    patterns.append(RegexPattern(
                        pattern=or_pattern,
                        description=f"Search for {mod1.display_name} OR {mod2.display_name}",
                        target_modifiers=[mod1.id, mod2.id],
                        expected_value=max(mod1.current_value, mod2.current_value),
                        synergy_bonus=synergy["bonus_value"],
                        confidence=synergy["confidence"],
                        specificity=0.6,  # Lower specificity for OR patterns
                        coverage=0.8,     # Higher coverage
                        priority=max(mod1.regex_priority, mod2.regex_priority),
                        pattern_type="combination_or",
                        optimization_level="synergy"
                    ))
                    
                    # Add AND pattern (more specific)
                    if len(and_pattern) <= self.max_pattern_length:
                        patterns.append(RegexPattern(
                            pattern=and_pattern,
                            description=f"Search for tablets with both {mod1.display_name} AND {mod2.display_name}",
                            target_modifiers=[mod1.id, mod2.id],
                            expected_value=mod1.current_value + mod2.current_value + synergy["bonus_value"],
                            synergy_bonus=synergy["bonus_value"],
                            confidence=synergy["confidence"],
                            specificity=0.9,  # High specificity for AND patterns
                            coverage=0.3,     # Lower coverage
                            priority=max(mod1.regex_priority, mod2.regex_priority) + 2,
                            pattern_type="combination_and",
                            optimization_level="precision"
                        ))
        
        return patterns
    
    async def _optimize_patterns(self, patterns: List[RegexPattern], max_patterns: int) -> List[RegexPattern]:
        """Optimize and select the best patterns."""
        if not patterns:
            return []
        
        # Calculate optimization scores
        for pattern in patterns:
            pattern.optimization_score = await self._calculate_optimization_score(pattern)
        
        # Sort by optimization score
        patterns.sort(key=lambda p: p.optimization_score, reverse=True)
        
        # Select top patterns, ensuring diversity
        selected_patterns = []
        used_modifiers = set()
        
        for pattern in patterns:
            if len(selected_patterns) >= max_patterns:
                break
            
            # Check for diversity (avoid too much overlap)
            pattern_modifiers = set(pattern.target_modifiers)
            overlap = len(pattern_modifiers.intersection(used_modifiers))
            
            # Allow pattern if it has low overlap or very high value
            if overlap <= 1 or pattern.optimization_score > 0.9:
                selected_patterns.append(pattern)
                used_modifiers.update(pattern_modifiers)
        
        return selected_patterns
    
    async def _calculate_optimization_score(self, pattern: RegexPattern) -> float:
        """Calculate optimization score for pattern ranking."""
        # Base score from expected value
        value_score = min(pattern.expected_value / 2.0, 1.0)  # Normalize to 0-1
        
        # Confidence factor
        confidence_factor = pattern.confidence
        
        # Specificity vs Coverage balance
        balance_score = (pattern.specificity + pattern.coverage) / 2
        
        # Priority bonus
        priority_bonus = min(pattern.priority / 10.0, 0.3)
        
        # Synergy bonus
        synergy_bonus = min(pattern.synergy_bonus / 0.5, 0.2) if pattern.synergy_bonus else 0
        
        # Combine scores
        total_score = (value_score * 0.4 + 
                      confidence_factor * 0.3 + 
                      balance_score * 0.2 + 
                      priority_bonus + 
                      synergy_bonus)
        
        return min(total_score, 1.0)
    
    async def _calculate_pattern_specificity(self, pattern: str) -> float:
        """Calculate how specific a regex pattern is."""
        # Simple heuristic based on pattern complexity
        specificity = 0.5  # Base specificity
        
        # More specific patterns have:
        if r'\b' in pattern:  # Word boundaries
            specificity += 0.1
        if r'(?=' in pattern:  # Lookaheads
            specificity += 0.2
        if len(pattern) > 20:  # Longer patterns
            specificity += 0.1
        if pattern.count('|') == 0:  # No OR operators
            specificity += 0.1
        
        return min(specificity, 1.0)
    
    async def _calculate_pattern_coverage(self, modifier: Modifier) -> float:
        """Calculate how much of the target space a pattern covers."""
        # Estimate based on modifier properties
        base_coverage = 0.7
        
        # High-value modifiers might be rarer
        if modifier.base_value > 1.0:
            base_coverage *= 0.8
        
        # Common categories have higher coverage
        if modifier.category in [ModifierCategory.UTILITY, ModifierCategory.DAMAGE]:
            base_coverage *= 1.1
        
        return min(base_coverage, 1.0)
    
    async def _calculate_coverage(self, patterns: List[RegexPattern]) -> float:
        """Calculate total coverage of pattern set."""
        if not patterns:
            return 0.0
        
        # Simple coverage calculation
        total_coverage = sum(p.coverage for p in patterns) / len(patterns)
        return min(total_coverage, 1.0)
    
    async def _estimate_expected_finds(self, patterns: List[RegexPattern], inventory_size: int) -> int:
        """Estimate expected number of valuable finds."""
        if not patterns:
            return 0
        
        # Estimate based on pattern coverage and inventory size
        avg_coverage = sum(p.coverage for p in patterns) / len(patterns)
        find_rate = avg_coverage * 0.1  # Assume 10% of covered items are valuable
        
        return max(1, int(inventory_size * find_rate))
    
    async def _generate_fallback_patterns(self, tablet_type_id: Optional[int]) -> List[RegexPattern]:
        """Generate simple fallback patterns for basic searches."""
        fallback_patterns = []
        
        # Generic high-value patterns
        fallback_patterns.append(RegexPattern(
            pattern=r"(increased|more|additional).*(damage|life|energy|mana)",
            description="Generic valuable modifier search",
            target_modifiers=[],
            expected_value=0.5,
            confidence=0.6,
            specificity=0.3,
            coverage=0.9,
            priority=1,
            pattern_type="fallback",
            optimization_level="basic"
        ))
        
        # Keystone pattern
        fallback_patterns.append(RegexPattern(
            pattern=r"(keystone|notable|passive)",
            description="Search for passive skill modifiers",
            target_modifiers=[],
            expected_value=0.8,
            confidence=0.7,
            specificity=0.6,
            coverage=0.5,
            priority=2,
            pattern_type="fallback",
            optimization_level="basic"
        ))
        
        return fallback_patterns
    
    async def _generate_optimization_notes(self, search_phases: List[Dict]) -> List[str]:
        """Generate optimization notes for the search strategy."""
        notes = []
        
        if len(search_phases) > 3:
            notes.append("Consider reducing search phases for faster execution")
        
        total_time = sum(phase.get("time_allocation", 0) for phase in search_phases)
        if total_time > 60:
            notes.append("Long search time - consider using more specific patterns")
        
        high_priority_phases = [p for p in search_phases if p.get("priority") == "critical"]
        if not high_priority_phases:
            notes.append("No critical priority patterns - may miss high-value items")
        
        return notes
