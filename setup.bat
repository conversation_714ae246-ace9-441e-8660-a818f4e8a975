@echo off
REM PoE 2 Tablet Optimizer - First Time Setup Script

echo ==========================================
echo  PoE 2 Tablet Optimizer - First Time Setup
echo ==========================================
echo.
echo This script will set up the PoE 2 Tablet Optimizer for first use.
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH!
    echo Please install Python 3.9+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation.
    echo.
    pause
    exit /b 1
)

echo Python found:
python --version
echo.

REM Check if virtual environment already exists
if exist ".venv" (
    echo Virtual environment already exists.
    echo.
    set /p recreate=Do you want to recreate it? This will delete existing packages. (y/n): 
    if /i "!recreate!"=="y" (
        echo Removing existing virtual environment...
        rmdir /s /q .venv
    ) else (
        echo Using existing virtual environment.
        goto activate_venv
    )
)

REM Create virtual environment
echo Creating virtual environment...
python -m venv .venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment!
    echo Make sure you have the 'venv' module available.
    pause
    exit /b 1
)
echo Virtual environment created successfully.
echo.

:activate_venv
REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment!
    pause
    exit /b 1
)

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo.
echo Installing core dependencies...
pip install -r requirements-simple.txt
if errorlevel 1 (
    echo ERROR: Failed to install core dependencies!
    pause
    exit /b 1
)

echo.
echo Installing additional ML packages...
pip install numpy pandas scikit-learn pydantic-settings
if errorlevel 1 (
    echo WARNING: Some ML packages failed to install. The app will still work for basic functionality.
)

REM Create logs directory
if not exist "logs" (
    echo Creating logs directory...
    mkdir logs
)

REM Test the installation
echo.
echo Testing installation...
python -c "from app.config import settings; print('✓ Configuration loaded successfully')"
if errorlevel 1 (
    echo ERROR: Configuration test failed!
    pause
    exit /b 1
)

python -c "import app.main; print('✓ Main application imported successfully')"
if errorlevel 1 (
    echo ERROR: Main application test failed!
    pause
    exit /b 1
)

echo.
echo ==========================================
echo  Setup Complete!
echo ==========================================
echo.
echo The PoE 2 Tablet Optimizer has been set up successfully.
echo.
echo To start the application:
echo   • Double-click 'start_server.bat' for simple startup
echo   • Double-click 'dev_server.bat' for development options
echo.
echo The application will be available at: http://localhost:8000
echo API documentation will be at: http://localhost:8000/docs
echo.
echo Press any key to exit...
pause >nul
