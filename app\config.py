"""
Configuration settings for the PoE 2 Tablet Optimizer application.
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Database
    database_url: str = "sqlite:///./poe_tablet_optimizer.db"
    
    # Redis (optional)
    redis_url: Optional[str] = None
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    debug: bool = True
    secret_key: str = "dev-secret-key-change-in-production"
    
    # PoE 2 Trade API
    poe2_trade_api_url: Optional[str] = None
    poe_session_id: Optional[str] = None
    
    # Data Collection
    data_update_interval_hours: int = 6
    market_data_retention_days: int = 365
    enable_predictive_analytics: bool = True
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    
    # External Services
    backup_storage_url: Optional[str] = None
    notification_webhook_url: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Derived configuration
class DatabaseConfig:
    """Database-specific configuration."""
    
    @property
    def is_sqlite(self) -> bool:
        return settings.database_url.startswith("sqlite")
    
    @property
    def is_postgresql(self) -> bool:
        return settings.database_url.startswith("postgresql")


class DataConfig:
    """Data processing configuration."""
    
    # Value tiers in exalted orbs
    HIGH_VALUE_THRESHOLD = 0.8
    MEDIUM_VALUE_THRESHOLD = 0.4
    LOW_VALUE_THRESHOLD = 0.1
    
    # Market analysis
    TREND_ANALYSIS_DAYS = 30
    VOLATILITY_THRESHOLD = 0.2
    CONFIDENCE_THRESHOLD = 0.7
    
    # Regex generation
    MAX_REGEX_PATTERNS = 10
    PATTERN_COMPLEXITY_LIMIT = 100


# Configuration instances
db_config = DatabaseConfig()
data_config = DataConfig()
