"""
API endpoints for regex pattern generation.
This is a core feature for generating patterns that can be used in PoE 2 inventory search.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.schemas.regex import RegexPatternResponse, RegexGenerationRequest
from app.services.regex_service import RegexService

router = APIRouter()


@router.get("/patterns/valuable", response_model=List[RegexPatternResponse])
async def get_valuable_patterns(
    tablet_type_id: int = Query(..., description="Tablet type to generate patterns for"),
    value_tier: str = Query("high", regex="^(high|medium|low|all)$", description="Value tier to include"),
    max_patterns: int = Query(5, le=10, description="Maximum number of patterns to generate"),
    include_trending: bool = Query(True, description="Include trending modifiers"),
    db: Session = Depends(get_db)
):
    """
    Generate regex patterns for valuable modifiers on a specific tablet type.
    This is the main endpoint for getting patterns to use in PoE 2 inventory search.
    """
    regex_service = RegexService(db)
    
    patterns = await regex_service.generate_valuable_patterns(
        tablet_type_id=tablet_type_id,
        value_tier=value_tier,
        max_patterns=max_patterns,
        include_trending=include_trending
    )
    
    return patterns


@router.post("/patterns/custom", response_model=List[RegexPatternResponse])
async def generate_custom_patterns(
    request: RegexGenerationRequest,
    db: Session = Depends(get_db)
):
    """
    Generate custom regex patterns based on specific criteria.
    Allows for more advanced pattern generation with custom parameters.
    """
    regex_service = RegexService(db)
    
    patterns = await regex_service.generate_custom_patterns(request)
    return patterns


@router.get("/patterns/trending", response_model=List[RegexPatternResponse])
async def get_trending_patterns(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    trend_direction: str = Query("up", regex="^(up|down)$", description="Trend direction"),
    days: int = Query(7, description="Trend analysis period"),
    max_patterns: int = Query(3, le=5, description="Maximum number of patterns"),
    db: Session = Depends(get_db)
):
    """Generate patterns for modifiers that are currently trending."""
    regex_service = RegexService(db)
    
    patterns = await regex_service.generate_trending_patterns(
        tablet_type_id=tablet_type_id,
        trend_direction=trend_direction,
        days=days,
        max_patterns=max_patterns
    )
    
    return patterns


@router.get("/patterns/synergy", response_model=List[RegexPatternResponse])
async def get_synergy_patterns(
    tablet_type_id: int = Query(..., description="Tablet type to generate patterns for"),
    min_synergy_bonus: float = Query(0.2, description="Minimum synergy bonus value"),
    max_patterns: int = Query(3, le=5, description="Maximum number of patterns"),
    db: Session = Depends(get_db)
):
    """Generate patterns for modifier combinations with high synergy bonuses."""
    regex_service = RegexService(db)
    
    patterns = await regex_service.generate_synergy_patterns(
        tablet_type_id=tablet_type_id,
        min_synergy_bonus=min_synergy_bonus,
        max_patterns=max_patterns
    )
    
    return patterns


@router.get("/patterns/test/{pattern_id}")
async def test_pattern(
    pattern_id: str,
    test_strings: str = Query(..., description="Comma-separated strings to test against pattern"),
    db: Session = Depends(get_db)
):
    """Test a regex pattern against sample strings."""
    regex_service = RegexService(db)
    
    test_list = [s.strip() for s in test_strings.split(",")]
    results = await regex_service.test_pattern(pattern_id, test_list)
    
    return {
        "pattern_id": pattern_id,
        "test_results": results,
        "matches_found": sum(1 for r in results if r["matches"]),
        "total_tests": len(results)
    }


@router.get("/patterns/export/{tablet_type_id}")
async def export_patterns(
    tablet_type_id: int,
    format: str = Query("text", regex="^(text|json|csv)$", description="Export format"),
    value_tier: str = Query("all", regex="^(high|medium|low|all)$", description="Value tier to include"),
    include_descriptions: bool = Query(True, description="Include pattern descriptions"),
    db: Session = Depends(get_db)
):
    """Export regex patterns in various formats for external use."""
    regex_service = RegexService(db)
    
    export_data = await regex_service.export_patterns(
        tablet_type_id=tablet_type_id,
        format=format,
        value_tier=value_tier,
        include_descriptions=include_descriptions
    )
    
    return export_data


@router.get("/modifiers/regex-ready")
async def get_regex_ready_modifiers(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    has_pattern: bool = Query(True, description="Only return modifiers with regex patterns"),
    min_priority: int = Query(0, description="Minimum regex priority"),
    db: Session = Depends(get_db)
):
    """Get modifiers that have regex patterns ready for use."""
    regex_service = RegexService(db)
    
    modifiers = await regex_service.get_regex_ready_modifiers(
        tablet_type_id=tablet_type_id,
        has_pattern=has_pattern,
        min_priority=min_priority
    )
    
    return modifiers


@router.post("/patterns/optimize")
async def optimize_patterns(
    tablet_type_id: int = Query(..., description="Tablet type to optimize patterns for"),
    target_pattern_count: int = Query(3, description="Target number of optimized patterns"),
    optimization_goal: str = Query("coverage", regex="^(coverage|precision|balance)$", description="Optimization goal"),
    db: Session = Depends(get_db)
):
    """
    Optimize regex patterns for maximum efficiency.
    Combines multiple patterns where possible and removes redundancy.
    """
    regex_service = RegexService(db)
    
    optimized = await regex_service.optimize_patterns(
        tablet_type_id=tablet_type_id,
        target_pattern_count=target_pattern_count,
        optimization_goal=optimization_goal
    )
    
    return {
        "message": "Pattern optimization completed",
        "original_pattern_count": optimized.get("original_count", 0),
        "optimized_pattern_count": len(optimized.get("patterns", [])),
        "coverage_improvement": optimized.get("coverage_improvement", 0),
        "patterns": optimized.get("patterns", [])
    }


@router.get("/usage/statistics")
async def get_usage_statistics(
    days_back: int = Query(30, description="Statistics period in days"),
    db: Session = Depends(get_db)
):
    """Get usage statistics for regex patterns."""
    regex_service = RegexService(db)
    
    stats = await regex_service.get_usage_statistics(days_back)
    return stats
