"""
Main FastAPI application for PoE 2 Tablet Crafting Profit Optimizer.
"""
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import logging
import os

from app.config import settings
from app.database import engine, Base
from app.api import tablets, modifiers, market, analytics, regex_generator

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(settings.log_file) if os.path.exists(os.path.dirname(settings.log_file)) else logging.StreamHandler(),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title="PoE 2 Tablet Crafting Profit Optimizer",
    description="Market intelligence tool for Path of Exile 2 tablet crafting with predictive analytics",
    version="0.1.0",
    debug=settings.debug
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.debug else ["http://localhost:8000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files and templates
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="app/templates")

# Include API routers
app.include_router(tablets.router, prefix="/api/tablets", tags=["tablets"])
app.include_router(modifiers.router, prefix="/api/modifiers", tags=["modifiers"])
app.include_router(market.router, prefix="/api/market", tags=["market"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["analytics"])
app.include_router(regex_generator.router, prefix="/api/regex", tags=["regex"])


@app.on_event("startup")
async def startup_event():
    """Initialize application on startup."""
    logger.info("Starting PoE 2 Tablet Optimizer application")
    
    # Create database tables
    Base.metadata.create_all(bind=engine)
    logger.info("Database tables created/verified")
    
    # Initialize data if needed
    # TODO: Add initial data loading logic
    
    logger.info("Application startup complete")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on application shutdown."""
    logger.info("Shutting down PoE 2 Tablet Optimizer application")


@app.get("/")
async def root(request: Request):
    """Main application page."""
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "0.1.0"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
