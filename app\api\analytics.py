"""
API endpoints for analytics and predictions.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.models.analytics import TrendAnalysis, PredictionModel
from app.schemas.analytics import TrendAnalysisResponse, PredictionModelResponse
from app.services.analytics_service import AnalyticsService

router = APIRouter()


@router.get("/predictions/{modifier_id}", response_model=TrendAnalysisResponse)
async def get_modifier_predictions(
    modifier_id: int,
    forecast_days: int = Query(30, le=90, description="Number of days to forecast"),
    db: Session = Depends(get_db)
):
    """Get price predictions for a specific modifier."""
    analytics_service = AnalyticsService(db)
    prediction = await analytics_service.get_modifier_prediction(modifier_id, forecast_days)
    
    if not prediction:
        raise HTTPException(status_code=404, detail="No prediction available for this modifier")
    
    return prediction


@router.get("/trends/analysis", response_model=List[TrendAnalysisResponse])
async def get_trend_analyses(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    trend_direction: Optional[str] = Query(None, regex="^(up|down|sideways)$", description="Filter by trend direction"),
    min_confidence: float = Query(0.6, description="Minimum confidence threshold"),
    days_back: int = Query(7, description="How many days back to look for analyses"),
    limit: int = Query(50, le=100, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """Get recent trend analyses with filtering."""
    analytics_service = AnalyticsService(db)
    analyses = await analytics_service.get_trend_analyses(
        tablet_type_id=tablet_type_id,
        trend_direction=trend_direction,
        min_confidence=min_confidence,
        days_back=days_back,
        limit=limit
    )
    return analyses


@router.get("/models", response_model=List[PredictionModelResponse])
async def get_prediction_models(
    model_type: Optional[str] = Query(None, description="Filter by model type"),
    is_active: bool = Query(True, description="Only return active models"),
    is_production: Optional[bool] = Query(None, description="Filter by production status"),
    db: Session = Depends(get_db)
):
    """Get available prediction models."""
    query = db.query(PredictionModel)
    
    if model_type:
        query = query.filter(PredictionModel.model_type == model_type)
    
    if is_active:
        query = query.filter(PredictionModel.is_active == True)
    
    if is_production is not None:
        query = query.filter(PredictionModel.is_production == is_production)
    
    models = query.all()
    return models


@router.get("/models/{model_id}", response_model=PredictionModelResponse)
async def get_prediction_model(
    model_id: int,
    db: Session = Depends(get_db)
):
    """Get details of a specific prediction model."""
    model = db.query(PredictionModel).filter(PredictionModel.id == model_id).first()
    if not model:
        raise HTTPException(status_code=404, detail="Prediction model not found")
    return model


@router.get("/recommendations/buy")
async def get_buy_recommendations(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    confidence_threshold: float = Query(0.7, description="Minimum confidence for recommendations"),
    max_risk: float = Query(0.3, description="Maximum acceptable risk level"),
    limit: int = Query(20, description="Maximum number of recommendations"),
    db: Session = Depends(get_db)
):
    """Get buy recommendations based on predictive analytics."""
    analytics_service = AnalyticsService(db)
    recommendations = await analytics_service.get_buy_recommendations(
        tablet_type_id=tablet_type_id,
        confidence_threshold=confidence_threshold,
        max_risk=max_risk,
        limit=limit
    )
    return recommendations


@router.get("/recommendations/sell")
async def get_sell_recommendations(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    confidence_threshold: float = Query(0.7, description="Minimum confidence for recommendations"),
    min_profit_margin: float = Query(0.1, description="Minimum profit margin"),
    limit: int = Query(20, description="Maximum number of recommendations"),
    db: Session = Depends(get_db)
):
    """Get sell recommendations based on predictive analytics."""
    analytics_service = AnalyticsService(db)
    recommendations = await analytics_service.get_sell_recommendations(
        tablet_type_id=tablet_type_id,
        confidence_threshold=confidence_threshold,
        min_profit_margin=min_profit_margin,
        limit=limit
    )
    return recommendations


@router.get("/performance/models")
async def get_model_performance(
    days_back: int = Query(30, description="Performance evaluation period"),
    db: Session = Depends(get_db)
):
    """Get performance metrics for all prediction models."""
    analytics_service = AnalyticsService(db)
    performance = await analytics_service.get_model_performance(days_back)
    return performance


@router.get("/insights/market-sentiment")
async def get_market_sentiment(
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    db: Session = Depends(get_db)
):
    """Get overall market sentiment analysis."""
    analytics_service = AnalyticsService(db)
    sentiment = await analytics_service.get_market_sentiment(tablet_type_id)
    return sentiment


@router.get("/insights/seasonal-patterns")
async def get_seasonal_patterns(
    modifier_id: Optional[int] = Query(None, description="Specific modifier to analyze"),
    tablet_type_id: Optional[int] = Query(None, description="Filter by tablet type"),
    db: Session = Depends(get_db)
):
    """Get seasonal pattern analysis."""
    analytics_service = AnalyticsService(db)
    patterns = await analytics_service.get_seasonal_patterns(modifier_id, tablet_type_id)
    return patterns


@router.post("/models/{model_id}/retrain")
async def retrain_model(
    model_id: int,
    force: bool = Query(False, description="Force retrain even if not due"),
    db: Session = Depends(get_db)
):
    """Trigger model retraining."""
    analytics_service = AnalyticsService(db)
    
    model = db.query(PredictionModel).filter(PredictionModel.id == model_id).first()
    if not model:
        raise HTTPException(status_code=404, detail="Prediction model not found")
    
    result = await analytics_service.retrain_model(model_id, force)
    return {
        "message": "Model retraining initiated",
        "model_id": model_id,
        "estimated_completion": result.get("estimated_completion"),
        "training_data_size": result.get("training_data_size")
    }


@router.get("/correlation/analysis")
async def get_correlation_analysis(
    modifier_ids: str = Query(..., description="Comma-separated modifier IDs to analyze"),
    days_back: int = Query(30, description="Analysis period in days"),
    db: Session = Depends(get_db)
):
    """Analyze correlations between modifier prices."""
    analytics_service = AnalyticsService(db)
    
    try:
        modifier_id_list = [int(x.strip()) for x in modifier_ids.split(",")]
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid modifier IDs format")
    
    if len(modifier_id_list) < 2:
        raise HTTPException(status_code=400, detail="At least 2 modifier IDs required for correlation analysis")
    
    correlation = await analytics_service.get_correlation_analysis(modifier_id_list, days_back)
    return correlation
