@echo off
REM PoE 2 Tablet Crafting Profit Optimizer - Server Startup Script
REM This batch file activates the virtual environment and starts the FastAPI server

echo ========================================
echo  PoE 2 Tablet Optimizer Server Startup
echo ========================================
echo.

REM Check if virtual environment exists
if not exist ".venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo Please make sure the .venv directory exists in the project root.
    echo.
    echo To create a virtual environment, run:
    echo   python -m venv .venv
    echo   .venv\Scripts\activate
    echo   pip install -r requirements-simple.txt
    echo.
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Check if activation was successful
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment!
    pause
    exit /b 1
)

echo Virtual environment activated successfully.
echo.

REM Check if required packages are installed
echo Checking if FastAPI is installed...
python -c "import fastapi; print('FastAPI found')" 2>nul
if errorlevel 1 (
    echo ERROR: FastAPI not found! Installing dependencies...
    echo.
    pip install -r requirements-simple.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies!
        pause
        exit /b 1
    )
)

REM Check if additional ML packages are needed
python -c "import numpy, pandas, scikit_learn" 2>nul
if errorlevel 1 (
    echo Installing additional ML packages...
    pip install numpy pandas scikit-learn pydantic-settings
)

echo.
echo Starting PoE 2 Tablet Optimizer server...
echo.
echo Server will be available at: http://localhost:8000
echo Press Ctrl+C to stop the server
echo.

REM Start the FastAPI server
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

REM If we get here, the server was stopped
echo.
echo Server stopped.
pause
