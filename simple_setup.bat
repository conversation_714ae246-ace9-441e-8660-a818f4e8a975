@echo off
REM PoE 2 Tablet Optimizer - Simple Setup Script

echo ==========================================
echo  PoE 2 Tablet Optimizer - Simple Setup
echo ==========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH!
    echo Please install Python 3.9+ from https://python.org
    echo.
    pause
    exit /b 1
)

echo Python found:
python --version
echo.

REM Create virtual environment if it doesn't exist
if not exist ".venv" (
    echo Creating virtual environment...
    python -m venv .venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment!
        pause
        exit /b 1
    )
    echo Virtual environment created.
) else (
    echo Virtual environment already exists.
)
echo.

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment!
    pause
    exit /b 1
)

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo.
echo Installing dependencies...
pip install -r requirements-simple.txt
pip install numpy pandas scikit-learn pydantic-settings

REM Create logs directory
if not exist "logs" (
    mkdir logs
)

REM Test installation
echo.
echo Testing installation...
python -c "from app.config import settings; print('Configuration OK')"
python -c "import app.main; print('Application OK')"

echo.
echo ==========================================
echo  Setup Complete!
echo ==========================================
echo.
echo You can now start the server with: start_server.bat
echo.
pause
