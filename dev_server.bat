@echo off
setlocal enabledelayedexpansion
REM PoE 2 Tablet Optimizer - Development Server Script
REM Enhanced version with development features

echo ==========================================
echo  PoE 2 Tablet Optimizer - Development Mode
echo ==========================================
echo.

REM Check if virtual environment exists
if not exist ".venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo.
    echo Would you like to create it now? (y/n)
    set /p create_venv=
    if /i "!create_venv!"=="y" (
        echo Creating virtual environment...
        python -m venv .venv
        if errorlevel 1 (
            echo ERROR: Failed to create virtual environment!
            pause
            exit /b 1
        )
        echo Virtual environment created successfully.
    ) else (
        echo Exiting...
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Install/update dependencies
echo.
echo Checking and installing dependencies...
pip install -r requirements-simple.txt
pip install numpy pandas scikit-learn pydantic-settings

REM Show menu
:menu
echo.
echo ==========================================
echo  Development Options
echo ==========================================
echo 1. Start server (development mode with auto-reload)
echo 2. Start server (production mode)
echo 3. Run tests
echo 4. Check code style
echo 5. View logs
echo 6. Open browser to application
echo 7. Exit
echo.
set /p choice=Enter your choice (1-7): 

if "%choice%"=="1" goto dev_server
if "%choice%"=="2" goto prod_server
if "%choice%"=="3" goto run_tests
if "%choice%"=="4" goto check_style
if "%choice%"=="5" goto view_logs
if "%choice%"=="6" goto open_browser
if "%choice%"=="7" goto exit
echo Invalid choice. Please try again.
goto menu

:dev_server
echo.
echo Starting development server with auto-reload...
echo Server will be available at: http://localhost:8000
echo API documentation at: http://localhost:8000/docs
echo Press Ctrl+C to stop the server
echo.
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
goto menu

:prod_server
echo.
echo Starting production server...
echo Server will be available at: http://localhost:8000
echo Press Ctrl+C to stop the server
echo.
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
goto menu

:run_tests
echo.
echo Running tests...
if exist "pytest.ini" (
    python -m pytest tests/ -v
) else (
    echo No tests configured yet.
)
echo.
pause
goto menu

:check_style
echo.
echo Checking code style...
echo Checking if black is installed...
pip show black >nul 2>&1
if errorlevel 1 (
    echo Installing black...
    pip install black
)
echo Running black formatter...
python -m black app/ --check --diff
echo.
pause
goto menu

:view_logs
echo.
echo Recent log entries:
if exist "logs\app.log" (
    type logs\app.log | more
) else (
    echo No log file found.
)
echo.
pause
goto menu

:open_browser
echo.
echo Opening browser...
start http://localhost:8000
goto menu

:exit
echo.
echo Goodbye!
pause
exit /b 0
