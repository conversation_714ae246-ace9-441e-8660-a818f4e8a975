/**
 * Interactive Charts and Visualizations for PoE 2 Tablet Optimizer
 */

const Charts = {
    // Chart instances storage
    instances: new Map(),
    
    // Default chart configuration
    defaultConfig: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            tooltip: {
                mode: 'index',
                intersect: false,
            }
        },
        scales: {
            x: {
                display: true,
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            y: {
                display: true,
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        },
        interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
        }
    },
    
    // Color schemes
    colors: {
        primary: '#4169e1',
        success: '#32cd32',
        warning: '#ff8c00',
        danger: '#dc143c',
        gold: '#c9aa71',
        purple: '#8a2be2',
        teal: '#20b2aa',
        pink: '#ff69b4'
    },
    
    // Create market overview chart
    createMarketOverview(canvasId, marketData) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        // Destroy existing chart
        this.destroyChart(canvasId);
        
        const config = {
            type: 'doughnut',
            data: {
                labels: ['Trending Up', 'Trending Down', 'Stable', 'High Volatility'],
                datasets: [{
                    data: [
                        marketData.trending_up_count || 0,
                        marketData.trending_down_count || 0,
                        (marketData.total_modifiers_tracked || 0) - 
                        (marketData.trending_up_count || 0) - 
                        (marketData.trending_down_count || 0) - 
                        (marketData.high_volatility_count || 0),
                        marketData.high_volatility_count || 0
                    ],
                    backgroundColor: [
                        this.colors.success,
                        this.colors.danger,
                        this.colors.primary,
                        this.colors.warning
                    ],
                    borderWidth: 2,
                    borderColor: '#1a1a1a'
                }]
            },
            options: {
                ...this.defaultConfig,
                plugins: {
                    ...this.defaultConfig.plugins,
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        };
        
        const chart = new Chart(ctx, config);
        this.instances.set(canvasId, chart);
        return chart;
    },
    
    // Create price trend chart
    createPriceTrendChart(canvasId, priceHistory, title = 'Price Trend') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        this.destroyChart(canvasId);
        
        const labels = priceHistory.map(item => 
            new Date(item.timestamp).toLocaleDateString()
        );
        const prices = priceHistory.map(item => item.price);
        const volumes = priceHistory.map(item => item.volume);
        
        const config = {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Price (ex)',
                        data: prices,
                        borderColor: this.colors.gold,
                        backgroundColor: this.colors.gold + '20',
                        tension: 0.1,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Volume',
                        data: volumes,
                        type: 'bar',
                        backgroundColor: this.colors.primary + '40',
                        borderColor: this.colors.primary,
                        borderWidth: 1,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                ...this.defaultConfig,
                plugins: {
                    ...this.defaultConfig.plugins,
                    title: {
                        display: true,
                        text: title
                    }
                },
                scales: {
                    x: this.defaultConfig.scales.x,
                    y: {
                        ...this.defaultConfig.scales.y,
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Price (ex)'
                        }
                    },
                    y1: {
                        ...this.defaultConfig.scales.y,
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Volume'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        };
        
        const chart = new Chart(ctx, config);
        this.instances.set(canvasId, chart);
        return chart;
    },
    
    // Create volatility heatmap
    createVolatilityHeatmap(canvasId, modifiers) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        this.destroyChart(canvasId);
        
        // Group modifiers by category and calculate average volatility
        const categoryData = {};
        modifiers.forEach(modifier => {
            const category = modifier.category || 'Unknown';
            if (!categoryData[category]) {
                categoryData[category] = {
                    volatilities: [],
                    values: []
                };
            }
            categoryData[category].volatilities.push(modifier.volatility || 0);
            categoryData[category].values.push(modifier.current_value || 0);
        });
        
        const labels = Object.keys(categoryData);
        const avgVolatilities = labels.map(category => {
            const volatilities = categoryData[category].volatilities;
            return volatilities.reduce((a, b) => a + b, 0) / volatilities.length;
        });
        const avgValues = labels.map(category => {
            const values = categoryData[category].values;
            return values.reduce((a, b) => a + b, 0) / values.length;
        });
        
        const config = {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Category Risk/Reward',
                    data: labels.map((label, index) => ({
                        x: avgVolatilities[index] * 100, // Convert to percentage
                        y: avgValues[index],
                        category: label
                    })),
                    backgroundColor: labels.map((_, index) => 
                        Object.values(this.colors)[index % Object.values(this.colors).length] + '80'
                    ),
                    borderColor: labels.map((_, index) => 
                        Object.values(this.colors)[index % Object.values(this.colors).length]
                    ),
                    borderWidth: 2,
                    pointRadius: 8,
                    pointHoverRadius: 12
                }]
            },
            options: {
                ...this.defaultConfig,
                plugins: {
                    ...this.defaultConfig.plugins,
                    title: {
                        display: true,
                        text: 'Risk vs Reward by Category'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const point = context.parsed;
                                const category = context.raw.category;
                                return `${category}: ${point.y.toFixed(2)} ex value, ${point.x.toFixed(1)}% volatility`;
                            }
                        }
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        ...this.defaultConfig.scales.x,
                        title: {
                            display: true,
                            text: 'Volatility (%)'
                        }
                    },
                    y: {
                        ...this.defaultConfig.scales.y,
                        title: {
                            display: true,
                            text: 'Average Value (ex)'
                        }
                    }
                }
            }
        };
        
        const chart = new Chart(ctx, config);
        this.instances.set(canvasId, chart);
        return chart;
    },
    
    // Create synergy analysis chart
    createSynergyChart(canvasId, synergyData) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        this.destroyChart(canvasId);
        
        const config = {
            type: 'radar',
            data: {
                labels: ['Damage', 'Defense', 'Utility', 'Resource', 'Special'],
                datasets: [{
                    label: 'Synergy Strength',
                    data: [
                        synergyData.damage || 0,
                        synergyData.defense || 0,
                        synergyData.utility || 0,
                        synergyData.resource || 0,
                        synergyData.special || 0
                    ],
                    backgroundColor: this.colors.purple + '40',
                    borderColor: this.colors.purple,
                    borderWidth: 2,
                    pointBackgroundColor: this.colors.purple,
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: this.colors.purple
                }]
            },
            options: {
                ...this.defaultConfig,
                plugins: {
                    ...this.defaultConfig.plugins,
                    title: {
                        display: true,
                        text: 'Modifier Category Synergies'
                    }
                },
                scales: {
                    r: {
                        angleLines: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        pointLabels: {
                            color: '#fff'
                        },
                        ticks: {
                            color: '#fff',
                            backdropColor: 'transparent'
                        }
                    }
                }
            }
        };
        
        const chart = new Chart(ctx, config);
        this.instances.set(canvasId, chart);
        return chart;
    },
    
    // Create profit opportunity chart
    createProfitChart(canvasId, opportunities) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        this.destroyChart(canvasId);
        
        const sortedOpportunities = opportunities
            .sort((a, b) => b.profit_estimate - a.profit_estimate)
            .slice(0, 10); // Top 10 opportunities
        
        const labels = sortedOpportunities.map(opp => opp.modifier_name || 'Unknown');
        const profits = sortedOpportunities.map(opp => opp.profit_estimate || 0);
        const confidences = sortedOpportunities.map(opp => opp.confidence || 0);
        
        const config = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Profit Estimate (ex)',
                        data: profits,
                        backgroundColor: profits.map(profit => 
                            profit > 0.5 ? this.colors.success + '80' : 
                            profit > 0.2 ? this.colors.warning + '80' : 
                            this.colors.danger + '80'
                        ),
                        borderColor: profits.map(profit => 
                            profit > 0.5 ? this.colors.success : 
                            profit > 0.2 ? this.colors.warning : 
                            this.colors.danger
                        ),
                        borderWidth: 1
                    }
                ]
            },
            options: {
                ...this.defaultConfig,
                plugins: {
                    ...this.defaultConfig.plugins,
                    title: {
                        display: true,
                        text: 'Top Profit Opportunities'
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                const confidence = confidences[context.dataIndex];
                                return `Confidence: ${(confidence * 100).toFixed(1)}%`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ...this.defaultConfig.scales.x,
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    y: {
                        ...this.defaultConfig.scales.y,
                        title: {
                            display: true,
                            text: 'Profit (ex)'
                        }
                    }
                }
            }
        };
        
        const chart = new Chart(ctx, config);
        this.instances.set(canvasId, chart);
        return chart;
    },
    
    // Create real-time market activity chart
    createMarketActivityChart(canvasId) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        this.destroyChart(canvasId);
        
        // Initialize with empty data
        const config = {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Market Activity',
                    data: [],
                    borderColor: this.colors.teal,
                    backgroundColor: this.colors.teal + '20',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                ...this.defaultConfig,
                animation: {
                    duration: 0 // Disable animation for real-time updates
                },
                plugins: {
                    ...this.defaultConfig.plugins,
                    title: {
                        display: true,
                        text: 'Real-time Market Activity'
                    }
                },
                scales: {
                    x: {
                        ...this.defaultConfig.scales.x,
                        type: 'time',
                        time: {
                            unit: 'minute'
                        }
                    },
                    y: {
                        ...this.defaultConfig.scales.y,
                        title: {
                            display: true,
                            text: 'Activity Level'
                        }
                    }
                }
            }
        };
        
        const chart = new Chart(ctx, config);
        this.instances.set(canvasId, chart);
        
        // Start real-time updates
        this.startRealTimeUpdates(canvasId);
        
        return chart;
    },
    
    // Start real-time chart updates
    startRealTimeUpdates(canvasId) {
        const chart = this.instances.get(canvasId);
        if (!chart) return;
        
        const updateInterval = setInterval(async () => {
            try {
                // Simulate real-time data (replace with actual API call)
                const now = new Date();
                const activity = Math.random() * 100;
                
                chart.data.labels.push(now);
                chart.data.datasets[0].data.push(activity);
                
                // Keep only last 20 data points
                if (chart.data.labels.length > 20) {
                    chart.data.labels.shift();
                    chart.data.datasets[0].data.shift();
                }
                
                chart.update('none'); // Update without animation
            } catch (error) {
                console.error('Real-time update failed:', error);
                clearInterval(updateInterval);
            }
        }, 5000); // Update every 5 seconds
        
        // Store interval for cleanup
        if (!this.intervals) this.intervals = new Set();
        this.intervals.add(updateInterval);
    },
    
    // Utility functions
    destroyChart(canvasId) {
        const existingChart = this.instances.get(canvasId);
        if (existingChart) {
            existingChart.destroy();
            this.instances.delete(canvasId);
        }
    },
    
    destroyAllCharts() {
        this.instances.forEach(chart => chart.destroy());
        this.instances.clear();
        
        if (this.intervals) {
            this.intervals.forEach(interval => clearInterval(interval));
            this.intervals.clear();
        }
    },
    
    // Update chart data
    updateChart(canvasId, newData) {
        const chart = this.instances.get(canvasId);
        if (!chart) return;
        
        chart.data = newData;
        chart.update();
    },
    
    // Export chart as image
    exportChart(canvasId, filename = 'chart.png') {
        const chart = this.instances.get(canvasId);
        if (!chart) return;
        
        const url = chart.toBase64Image();
        const link = document.createElement('a');
        link.download = filename;
        link.href = url;
        link.click();
    }
};

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Auto-initialize charts based on canvas elements with data attributes
    document.querySelectorAll('canvas[data-chart-type]').forEach(canvas => {
        const chartType = canvas.dataset.chartType;
        const chartData = canvas.dataset.chartData ? JSON.parse(canvas.dataset.chartData) : {};
        
        switch (chartType) {
            case 'market-overview':
                Charts.createMarketOverview(canvas.id, chartData);
                break;
            case 'price-trend':
                Charts.createPriceTrendChart(canvas.id, chartData);
                break;
            case 'volatility-heatmap':
                Charts.createVolatilityHeatmap(canvas.id, chartData);
                break;
            case 'synergy':
                Charts.createSynergyChart(canvas.id, chartData);
                break;
            case 'profit':
                Charts.createProfitChart(canvas.id, chartData);
                break;
            case 'market-activity':
                Charts.createMarketActivityChart(canvas.id);
                break;
        }
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    Charts.destroyAllCharts();
});

// Export for global access
window.Charts = Charts;
