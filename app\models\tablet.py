"""
Tablet and tablet type models.
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, ForeignKey, DateTime, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class TabletType(Base):
    """
    Represents different types of tablets in PoE 2.
    Each tablet type has unique modifier pools and base properties.
    """
    __tablename__ = "tablet_types"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    display_name = Column(String(150), nullable=False)
    description = Column(Text)
    base_cost = Column(Float, default=0.0)  # Base cost in exalted orbs
    crafting_cost = Column(Float, default=0.0)  # Average crafting cost
    is_active = Column(Boolean, default=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tablets = relationship("Tablet", back_populates="tablet_type")
    modifiers = relationship("Modifier", back_populates="tablet_type")
    
    def __repr__(self):
        return f"<TabletType(name='{self.name}', display_name='{self.display_name}')>"


class Tablet(Base):
    """
    Represents a specific tablet instance with modifiers.
    Used for manual evaluation and historical tracking.
    """
    __tablename__ = "tablets"
    
    id = Column(Integer, primary_key=True, index=True)
    tablet_type_id = Column(Integer, ForeignKey("tablet_types.id"), nullable=False)
    
    # Modifiers (typically 2 for magic tablets)
    modifier1_id = Column(Integer, ForeignKey("modifiers.id"))
    modifier1_tier = Column(Integer, default=1)
    modifier2_id = Column(Integer, ForeignKey("modifiers.id"))
    modifier2_tier = Column(Integer, default=1)
    
    # Calculated values
    base_value = Column(Float, default=0.0)  # Individual modifier values
    synergy_bonus = Column(Float, default=0.0)  # Bonus for modifier combinations
    total_value = Column(Float, default=0.0)  # Total market value
    profit_estimate = Column(Float, default=0.0)  # Estimated profit
    
    # Market data
    market_confidence = Column(Float, default=0.0)  # Confidence in valuation (0-1)
    last_market_check = Column(DateTime(timezone=True))
    
    # User data
    user_notes = Column(Text)
    is_sold = Column(Boolean, default=False)
    actual_sale_price = Column(Float)
    sale_date = Column(DateTime(timezone=True))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tablet_type = relationship("TabletType", back_populates="tablets")
    modifier1 = relationship("Modifier", foreign_keys=[modifier1_id])
    modifier2 = relationship("Modifier", foreign_keys=[modifier2_id])
    
    def __repr__(self):
        return f"<Tablet(id={self.id}, type='{self.tablet_type.name if self.tablet_type else 'Unknown'}', value={self.total_value})>"
    
    @property
    def is_profitable(self) -> bool:
        """Check if tablet is profitable to sell."""
        return self.profit_estimate > 0
    
    @property
    def value_tier(self) -> str:
        """Get value tier classification."""
        from app.config import data_config
        if self.total_value >= data_config.HIGH_VALUE_THRESHOLD:
            return "high"
        elif self.total_value >= data_config.MEDIUM_VALUE_THRESHOLD:
            return "medium"
        else:
            return "low"
