#!/usr/bin/env python3
"""
Startup script for PoE 2 Tablet Optimizer.
Handles initialization, health checks, and graceful startup.
"""
import os
import sys
import time
import subprocess
import asyncio
import logging
from pathlib import Path
from typing import Optional

# Add app directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config import settings
from app.database import engine, SessionLocal
from app.models import Base


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class StartupManager:
    """Manages application startup process."""
    
    def __init__(self):
        self.startup_checks = [
            self.check_environment,
            self.check_database_connection,
            self.run_migrations,
            self.load_initial_data,
            self.verify_poe_api_access,
            self.start_background_services
        ]
    
    async def run_startup_sequence(self):
        """Run the complete startup sequence."""
        logger.info("🚀 Starting PoE 2 Tablet Optimizer...")
        
        for i, check in enumerate(self.startup_checks, 1):
            logger.info(f"Step {i}/{len(self.startup_checks)}: {check.__name__}")
            
            try:
                success = await check()
                if not success:
                    logger.error(f"❌ Startup failed at step: {check.__name__}")
                    return False
                logger.info(f"✅ {check.__name__} completed")
            except Exception as e:
                logger.error(f"💥 Error in {check.__name__}: {e}")
                return False
        
        logger.info("🎉 Startup sequence completed successfully!")
        return True
    
    async def check_environment(self) -> bool:
        """Check environment variables and configuration."""
        required_vars = [
            'DATABASE_URL',
            'SECRET_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(settings, var.lower(), None):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"Missing required environment variables: {missing_vars}")
            return False
        
        # Check optional but important variables
        if not settings.poe_session_id:
            logger.warning("POE_SESSION_ID not set - PoE API features will be limited")
        
        # Create necessary directories
        directories = ['logs', 'static/uploads', 'data/cache']
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        return True
    
    async def check_database_connection(self) -> bool:
        """Check database connectivity."""
        try:
            # Test connection
            with engine.connect() as conn:
                result = conn.execute("SELECT 1").scalar()
                if result != 1:
                    logger.error("Database connection test failed")
                    return False
            
            logger.info("Database connection successful")
            return True
            
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            logger.info("Waiting 5 seconds before retry...")
            await asyncio.sleep(5)
            
            # Retry once
            try:
                with engine.connect() as conn:
                    conn.execute("SELECT 1").scalar()
                logger.info("Database connection successful on retry")
                return True
            except Exception as retry_error:
                logger.error(f"Database connection retry failed: {retry_error}")
                return False
    
    async def run_migrations(self) -> bool:
        """Run database migrations."""
        try:
            # Check if alembic is available
            result = subprocess.run(['alembic', '--version'], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.warning("Alembic not available, creating tables directly")
                # Create tables directly
                Base.metadata.create_all(bind=engine)
                return True
            
            # Run alembic migrations
            result = subprocess.run(['alembic', 'upgrade', 'head'], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Migration failed: {result.stderr}")
                return False
            
            logger.info("Database migrations completed")
            return True
            
        except Exception as e:
            logger.error(f"Migration error: {e}")
            return False
    
    async def load_initial_data(self) -> bool:
        """Load initial data if database is empty."""
        try:
            db = SessionLocal()
            
            # Check if data already exists
            from app.models.tablet import TabletType
            existing_tablets = db.query(TabletType).count()
            
            if existing_tablets > 0:
                logger.info("Initial data already exists, skipping load")
                db.close()
                return True
            
            db.close()
            
            # Run initial data loading script
            script_path = Path(__file__).parent / 'load_initial_data.py'
            if script_path.exists():
                result = subprocess.run([sys.executable, str(script_path)], 
                                      capture_output=True, text=True)
                
                if result.returncode != 0:
                    logger.error(f"Initial data loading failed: {result.stderr}")
                    return False
                
                logger.info("Initial data loaded successfully")
            else:
                logger.warning("Initial data script not found, skipping")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading initial data: {e}")
            return False
    
    async def verify_poe_api_access(self) -> bool:
        """Verify PoE API access."""
        if not settings.poe_session_id:
            logger.warning("No PoE session ID configured, skipping API verification")
            return True
        
        try:
            from app.services.market_service import MarketService
            
            market_service = MarketService()
            
            # Test API access with a simple request
            # This is a basic connectivity test
            logger.info("Testing PoE API connectivity...")
            
            # For now, just verify the service can be instantiated
            # In a real implementation, you'd make a test API call
            
            logger.info("PoE API access verified")
            return True
            
        except Exception as e:
            logger.error(f"PoE API verification failed: {e}")
            logger.warning("Continuing without PoE API access")
            return True  # Non-critical failure
    
    async def start_background_services(self) -> bool:
        """Start background services."""
        try:
            # Import and start real-time services
            from app.services.realtime_service import start_real_time_services
            
            logger.info("Starting background services...")
            
            # Start real-time services in background
            asyncio.create_task(start_real_time_services())
            
            logger.info("Background services started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start background services: {e}")
            return False


def check_port_available(port: int) -> bool:
    """Check if a port is available."""
    import socket
    
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return True
        except OSError:
            return False


def wait_for_service(host: str, port: int, timeout: int = 30) -> bool:
    """Wait for a service to become available."""
    import socket
    
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex((host, port))
                if result == 0:
                    return True
        except Exception:
            pass
        
        time.sleep(1)
    
    return False


async def main():
    """Main startup function."""
    startup_manager = StartupManager()
    
    # Run startup sequence
    success = await startup_manager.run_startup_sequence()
    
    if not success:
        logger.error("❌ Startup failed!")
        sys.exit(1)
    
    # Check if port is available
    port = getattr(settings, 'port', 8000)
    if not check_port_available(port):
        logger.error(f"Port {port} is already in use!")
        sys.exit(1)
    
    # Start the application
    logger.info(f"🌟 Starting application on port {port}...")
    
    try:
        # Import here to avoid circular imports
        import uvicorn
        from app.main import app
        
        # Configure uvicorn
        config = uvicorn.Config(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info",
            reload=settings.debug,
            access_log=True
        )
        
        server = uvicorn.Server(config)
        await server.serve()
        
    except KeyboardInterrupt:
        logger.info("🛑 Application stopped by user")
    except Exception as e:
        logger.error(f"💥 Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Handle command line arguments
    import argparse
    
    parser = argparse.ArgumentParser(description="PoE 2 Tablet Optimizer Startup")
    parser.add_argument("--skip-checks", action="store_true", 
                       help="Skip startup checks and start immediately")
    parser.add_argument("--port", type=int, default=8000,
                       help="Port to run the application on")
    parser.add_argument("--host", default="0.0.0.0",
                       help="Host to bind the application to")
    
    args = parser.parse_args()
    
    if args.skip_checks:
        logger.info("⚡ Skipping startup checks...")
        import uvicorn
        from app.main import app
        
        uvicorn.run(
            app,
            host=args.host,
            port=args.port,
            log_level="info",
            reload=settings.debug
        )
    else:
        # Run full startup sequence
        asyncio.run(main())
