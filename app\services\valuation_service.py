"""
Advanced valuation engine for tablet profit optimization.
This service implements sophisticated algorithms for calculating tablet values,
modifier synergies, and profit predictions.
"""
from sqlalchemy.orm import Session
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import math
import logging

from app.models.tablet import Tablet, TabletType
from app.models.modifier import Modifier, ModifierValue, ModifierCategory
from app.models.market import MarketData, MarketTrend
from app.config import data_config

logger = logging.getLogger(__name__)


class SynergyCalculator:
    """Advanced synergy calculation system."""
    
    # Synergy matrices for different modifier combinations
    CATEGORY_SYNERGIES = {
        (ModifierCategory.DAMAGE, ModifierCategory.UTILITY): 0.15,
        (ModifierCategory.DAMAGE, ModifierCategory.RESOURCE): 0.12,
        (ModifierCategory.DEFENSE, ModifierCategory.RESOURCE): 0.18,
        (ModifierCategory.DEFENSE, ModifierCategory.UTILITY): 0.10,
        (ModifierCategory.UTILITY, ModifierCategory.SPECIAL): 0.20,
        (ModifierCategory.DAMAGE, ModifierCategory.SPECIAL): 0.25,
        (ModifierCategory.DEFENSE, ModifierCategory.SPECIAL): 0.15,
        (ModifierCategory.RESOURCE, ModifierCategory.SPECIAL): 0.12,
        # Same category synergies
        (ModifierCategory.DAMAGE, ModifierCategory.DAMAGE): 0.08,
        (ModifierCategory.DEFENSE, ModifierCategory.DEFENSE): 0.10,
        (ModifierCategory.UTILITY, ModifierCategory.UTILITY): 0.06,
        (ModifierCategory.RESOURCE, ModifierCategory.RESOURCE): 0.05,
        (ModifierCategory.SPECIAL, ModifierCategory.SPECIAL): 0.30,
    }
    
    # Anti-synergy penalties
    ANTI_SYNERGIES = {
        (ModifierCategory.DAMAGE, ModifierCategory.DEFENSE): -0.05,
    }
    
    # Specific modifier name synergies (examples)
    SPECIFIC_SYNERGIES = {
        ("eldritch_battery", "ghost_reaver"): 0.40,  # Energy Shield synergy
        ("master_of_arms", "deep_thoughts"): 0.15,   # Weapon + Mana synergy
        ("breach_splinter_chance", "breach_monster_life"): -0.10,  # Risk vs reward
    }
    
    def calculate_synergy(self, modifier1: Modifier, modifier2: Modifier) -> Dict[str, Any]:
        """Calculate comprehensive synergy between two modifiers."""
        if not modifier1 or not modifier2:
            return self._create_synergy_result(0.0, "none", "No modifiers provided")
        
        if modifier1.tablet_type_id != modifier2.tablet_type_id:
            return self._create_synergy_result(0.0, "incompatible", "Different tablet types")
        
        # Check specific modifier synergies first
        specific_synergy = self._check_specific_synergies(modifier1, modifier2)
        if specific_synergy is not None:
            return specific_synergy
        
        # Check category synergies
        category_synergy = self._calculate_category_synergy(modifier1, modifier2)
        
        # Apply value scaling
        base_value = modifier1.current_value + modifier2.current_value
        synergy_value = base_value * category_synergy["multiplier"]
        
        # Apply market confidence scaling
        confidence_factor = self._calculate_confidence_factor(modifier1, modifier2)
        final_value = synergy_value * confidence_factor
        
        return self._create_synergy_result(
            final_value,
            category_synergy["type"],
            category_synergy["description"],
            confidence_factor
        )
    
    def _check_specific_synergies(self, mod1: Modifier, mod2: Modifier) -> Optional[Dict[str, Any]]:
        """Check for specific modifier name synergies."""
        pair1 = (mod1.name, mod2.name)
        pair2 = (mod2.name, mod1.name)
        
        multiplier = self.SPECIFIC_SYNERGIES.get(pair1, self.SPECIFIC_SYNERGIES.get(pair2))
        
        if multiplier is not None:
            base_value = mod1.current_value + mod2.current_value
            synergy_value = base_value * abs(multiplier)
            
            if multiplier > 0:
                return self._create_synergy_result(
                    synergy_value, "specific_positive", 
                    f"Strong synergy between {mod1.display_name} and {mod2.display_name}"
                )
            else:
                return self._create_synergy_result(
                    -synergy_value, "specific_negative",
                    f"Anti-synergy between {mod1.display_name} and {mod2.display_name}"
                )
        
        return None
    
    def _calculate_category_synergy(self, mod1: Modifier, mod2: Modifier) -> Dict[str, Any]:
        """Calculate synergy based on modifier categories."""
        cat1, cat2 = mod1.category, mod2.category
        
        # Check positive synergies
        multiplier = self.CATEGORY_SYNERGIES.get((cat1, cat2), 
                                               self.CATEGORY_SYNERGIES.get((cat2, cat1), 0.0))
        
        if multiplier > 0:
            if cat1 == cat2:
                return {
                    "multiplier": multiplier,
                    "type": "same_category",
                    "description": f"Same category synergy ({cat1.value})"
                }
            else:
                return {
                    "multiplier": multiplier,
                    "type": "complementary",
                    "description": f"Complementary synergy ({cat1.value} + {cat2.value})"
                }
        
        # Check anti-synergies
        anti_multiplier = self.ANTI_SYNERGIES.get((cat1, cat2),
                                                self.ANTI_SYNERGIES.get((cat2, cat1), 0.0))
        
        if anti_multiplier < 0:
            return {
                "multiplier": abs(anti_multiplier),
                "type": "anti_synergy",
                "description": f"Anti-synergy penalty ({cat1.value} + {cat2.value})"
            }
        
        # No synergy
        return {
            "multiplier": 0.02,  # Small base synergy for any two modifiers
            "type": "neutral",
            "description": "Neutral combination"
        }
    
    def _calculate_confidence_factor(self, mod1: Modifier, mod2: Modifier) -> float:
        """Calculate confidence factor based on market data quality."""
        confidence1 = self._get_modifier_confidence(mod1)
        confidence2 = self._get_modifier_confidence(mod2)
        
        # Use geometric mean for combined confidence
        combined_confidence = math.sqrt(confidence1 * confidence2)
        
        # Scale confidence factor (0.5 to 1.0 range)
        return 0.5 + (combined_confidence * 0.5)
    
    def _get_modifier_confidence(self, modifier: Modifier) -> float:
        """Get confidence score for a modifier's market data."""
        if hasattr(modifier, 'modifier_values') and modifier.modifier_values:
            latest_value = max(modifier.modifier_values, key=lambda x: x.created_at)
            return latest_value.confidence
        return 0.5  # Default confidence
    
    def _create_synergy_result(self, value: float, synergy_type: str, description: str, 
                             confidence: float = 0.8) -> Dict[str, Any]:
        """Create standardized synergy result."""
        return {
            "bonus_value": value,
            "synergy_type": synergy_type,
            "description": description,
            "confidence": confidence,
            "strength": min(abs(value) / 0.5, 1.0) if value != 0 else 0.0  # Normalize to 0-1
        }


class ValuationEngine:
    """Core valuation engine for tablet profit calculations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.synergy_calculator = SynergyCalculator()
    
    def calculate_tablet_value(self, tablet_type: TabletType, modifier1: Optional[Modifier] = None,
                             modifier2: Optional[Modifier] = None, tier1: int = 1, tier2: int = 1) -> Dict[str, Any]:
        """
        Calculate comprehensive tablet value with advanced algorithms.
        
        Returns detailed valuation breakdown including:
        - Base modifier values
        - Tier adjustments
        - Synergy bonuses
        - Market confidence
        - Profit estimates
        - Risk assessment
        """
        valuation = {
            "tablet_type": tablet_type.display_name,
            "base_cost": tablet_type.base_cost,
            "crafting_cost": tablet_type.crafting_cost,
            "total_cost": tablet_type.base_cost + tablet_type.crafting_cost,
            "modifier1_value": 0.0,
            "modifier2_value": 0.0,
            "tier_adjustments": 0.0,
            "synergy_bonus": 0.0,
            "market_adjustments": 0.0,
            "total_value": 0.0,
            "profit_estimate": 0.0,
            "confidence_score": 0.0,
            "risk_level": "unknown",
            "recommendation": "hold",
            "breakdown": []
        }
        
        # Calculate base modifier values
        if modifier1:
            mod1_value = self._calculate_modifier_value(modifier1, tier1)
            valuation["modifier1_value"] = mod1_value["adjusted_value"]
            valuation["breakdown"].append({
                "component": "Modifier 1",
                "name": modifier1.display_name,
                "base_value": modifier1.current_value,
                "tier_multiplier": mod1_value["tier_multiplier"],
                "final_value": mod1_value["adjusted_value"]
            })
        
        if modifier2:
            mod2_value = self._calculate_modifier_value(modifier2, tier2)
            valuation["modifier2_value"] = mod2_value["adjusted_value"]
            valuation["breakdown"].append({
                "component": "Modifier 2",
                "name": modifier2.display_name,
                "base_value": modifier2.current_value,
                "tier_multiplier": mod2_value["tier_multiplier"],
                "final_value": mod2_value["adjusted_value"]
            })
        
        # Calculate synergy bonus
        if modifier1 and modifier2:
            synergy = self.synergy_calculator.calculate_synergy(modifier1, modifier2)
            valuation["synergy_bonus"] = synergy["bonus_value"]
            valuation["breakdown"].append({
                "component": "Synergy Bonus",
                "name": synergy["description"],
                "base_value": 0.0,
                "tier_multiplier": 1.0,
                "final_value": synergy["bonus_value"]
            })
        
        # Apply market adjustments
        market_adjustment = self._calculate_market_adjustments(modifier1, modifier2)
        valuation["market_adjustments"] = market_adjustment["total_adjustment"]
        valuation["confidence_score"] = market_adjustment["confidence"]
        
        # Calculate totals
        base_total = valuation["modifier1_value"] + valuation["modifier2_value"]
        valuation["total_value"] = base_total + valuation["synergy_bonus"] + valuation["market_adjustments"]
        valuation["profit_estimate"] = valuation["total_value"] - valuation["total_cost"]
        
        # Risk assessment
        risk_assessment = self._assess_risk(valuation, modifier1, modifier2)
        valuation["risk_level"] = risk_assessment["level"]
        valuation["risk_factors"] = risk_assessment["factors"]
        
        # Generate recommendation
        valuation["recommendation"] = self._generate_recommendation(valuation)
        
        return valuation
    
    def _calculate_modifier_value(self, modifier: Modifier, tier: int) -> Dict[str, Any]:
        """Calculate value for a single modifier with tier adjustments."""
        base_value = modifier.current_value
        
        # Tier multiplier calculation
        tier_multiplier = self._calculate_tier_multiplier(modifier, tier)
        
        # Market trend adjustment
        trend_multiplier = self._calculate_trend_multiplier(modifier)
        
        # Volatility adjustment
        volatility_adjustment = self._calculate_volatility_adjustment(modifier)
        
        adjusted_value = base_value * tier_multiplier * trend_multiplier * volatility_adjustment
        
        return {
            "base_value": base_value,
            "tier_multiplier": tier_multiplier,
            "trend_multiplier": trend_multiplier,
            "volatility_adjustment": volatility_adjustment,
            "adjusted_value": adjusted_value
        }
    
    def _calculate_tier_multiplier(self, modifier: Modifier, tier: int) -> float:
        """Calculate tier-based value multiplier."""
        if tier <= 0:
            return 1.0
        
        # Base multiplier increases with tier
        base_multiplier = 1.0
        
        # Different categories have different tier scaling
        if modifier.category == ModifierCategory.DAMAGE:
            tier_bonus = (tier - 1) * 0.25  # 25% per tier for damage
        elif modifier.category == ModifierCategory.DEFENSE:
            tier_bonus = (tier - 1) * 0.20  # 20% per tier for defense
        elif modifier.category == ModifierCategory.UTILITY:
            tier_bonus = (tier - 1) * 0.15  # 15% per tier for utility
        elif modifier.category == ModifierCategory.RESOURCE:
            tier_bonus = (tier - 1) * 0.18  # 18% per tier for resource
        elif modifier.category == ModifierCategory.SPECIAL:
            tier_bonus = (tier - 1) * 0.30  # 30% per tier for special
        else:
            tier_bonus = (tier - 1) * 0.20  # Default 20% per tier
        
        return base_multiplier + tier_bonus
    
    def _calculate_trend_multiplier(self, modifier: Modifier) -> float:
        """Calculate trend-based value adjustment."""
        # Get market data for trend analysis
        market_data = self.db.query(MarketData).filter(
            MarketData.modifier_id == modifier.id
        ).first()
        
        if not market_data:
            return 1.0
        
        # Adjust based on recent price trends
        trend_factor = 1.0
        
        if market_data.price_change_24h > 10:
            trend_factor = 1.1  # Strong upward trend
        elif market_data.price_change_24h > 5:
            trend_factor = 1.05  # Moderate upward trend
        elif market_data.price_change_24h < -10:
            trend_factor = 0.9  # Strong downward trend
        elif market_data.price_change_24h < -5:
            trend_factor = 0.95  # Moderate downward trend
        
        return trend_factor
    
    def _calculate_volatility_adjustment(self, modifier: Modifier) -> float:
        """Calculate volatility-based risk adjustment."""
        market_data = self.db.query(MarketData).filter(
            MarketData.modifier_id == modifier.id
        ).first()
        
        if not market_data:
            return 1.0
        
        # High volatility reduces confidence in valuation
        volatility = market_data.volatility_score
        
        if volatility > 0.5:
            return 0.9  # High volatility penalty
        elif volatility > 0.3:
            return 0.95  # Moderate volatility penalty
        else:
            return 1.0  # Low volatility, no adjustment
    
    def _calculate_market_adjustments(self, modifier1: Optional[Modifier], 
                                    modifier2: Optional[Modifier]) -> Dict[str, Any]:
        """Calculate market-based adjustments."""
        total_adjustment = 0.0
        confidence_scores = []
        
        for modifier in [modifier1, modifier2]:
            if modifier:
                market_data = self.db.query(MarketData).filter(
                    MarketData.modifier_id == modifier.id
                ).first()
                
                if market_data:
                    # Liquidity adjustment
                    if market_data.liquidity_score > 0.8:
                        total_adjustment += modifier.current_value * 0.05  # High liquidity bonus
                    elif market_data.liquidity_score < 0.3:
                        total_adjustment -= modifier.current_value * 0.05  # Low liquidity penalty
                    
                    confidence_scores.append(market_data.data_quality)
                else:
                    confidence_scores.append(0.5)
        
        average_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.5
        
        return {
            "total_adjustment": total_adjustment,
            "confidence": average_confidence
        }
    
    def _assess_risk(self, valuation: Dict[str, Any], modifier1: Optional[Modifier], 
                    modifier2: Optional[Modifier]) -> Dict[str, Any]:
        """Assess risk factors for the tablet valuation."""
        risk_factors = []
        risk_score = 0.0
        
        # Profit margin risk
        profit_margin = valuation["profit_estimate"] / valuation["total_cost"] if valuation["total_cost"] > 0 else 0
        if profit_margin < 0.1:
            risk_factors.append("Low profit margin")
            risk_score += 0.3
        
        # Market confidence risk
        if valuation["confidence_score"] < 0.6:
            risk_factors.append("Low market data confidence")
            risk_score += 0.2
        
        # Volatility risk
        for modifier in [modifier1, modifier2]:
            if modifier:
                market_data = self.db.query(MarketData).filter(
                    MarketData.modifier_id == modifier.id
                ).first()
                
                if market_data and market_data.volatility_score > 0.4:
                    risk_factors.append(f"High volatility in {modifier.display_name}")
                    risk_score += 0.15
        
        # Determine risk level
        if risk_score >= 0.6:
            risk_level = "high"
        elif risk_score >= 0.3:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        return {
            "level": risk_level,
            "score": risk_score,
            "factors": risk_factors
        }
    
    def _generate_recommendation(self, valuation: Dict[str, Any]) -> str:
        """Generate trading recommendation based on valuation."""
        profit_margin = valuation["profit_estimate"] / valuation["total_cost"] if valuation["total_cost"] > 0 else 0
        confidence = valuation["confidence_score"]
        risk_level = valuation["risk_level"]
        
        if profit_margin > 0.3 and confidence > 0.7 and risk_level == "low":
            return "strong_buy"
        elif profit_margin > 0.15 and confidence > 0.6 and risk_level in ["low", "medium"]:
            return "buy"
        elif profit_margin > 0.05 and confidence > 0.5:
            return "weak_buy"
        elif profit_margin < -0.1 or risk_level == "high":
            return "sell"
        else:
            return "hold"
