# PoE 2 Tablet Crafting Profit Optimizer

A comprehensive market intelligence tool for Path of Exile 2 tablet crafting that provides instant sell/recycle decisions with predictive analytics.

## 🎯 Features

- **Instant Modifier Valuation** - Real-time market-based pricing for all tablet modifiers
- **Predictive Market Analytics** - ML-powered trend analysis and price forecasting
- **Regex Pattern Generator** - Auto-generate patterns for PoE 2 inventory search
- **Interactive Visualizations** - Charts showing market trends and modifier performance
- **Manual Evaluation Calculator** - Detailed profit analysis for specific tablets
- **Real-time Market Intelligence** - Live updates and market change alerts

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- PostgreSQL (optional, SQLite for development)
- Redis (optional, for caching)

### Installation

1. **Clone and setup environment:**
```bash
git clone <repository-url>
cd POE_Tablet_Price
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Environment configuration:**
```bash
cp .env.example .env
# Edit .env with your database and API configurations
```

3. **Database setup:**
```bash
# Initialize database
python -m alembic upgrade head

# Load initial data
python scripts/load_initial_data.py
```

4. **Run the application:**
```bash
uvicorn app.main:app --reload
```

Visit `http://localhost:8000` to access the application.

## 📁 Project Structure

```
POE_Tablet_Price/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI application entry point
│   ├── config.py            # Configuration settings
│   ├── database.py          # Database connection and setup
│   ├── models/              # SQLAlchemy models
│   ├── schemas/             # Pydantic schemas for API
│   ├── api/                 # API route handlers
│   ├── services/            # Business logic services
│   ├── utils/               # Utility functions
│   └── templates/           # Jinja2 HTML templates
├── data/
│   ├── raw/                 # Raw data files
│   ├── processed/           # Processed data
│   └── exports/             # Generated exports
├── scripts/
│   ├── data_collection.py   # Market data collection
│   ├── load_initial_data.py # Initial data loading
│   └── update_prices.py     # Price update automation
├── tests/
│   ├── test_api.py
│   ├── test_models.py
│   └── test_services.py
├── static/                  # CSS, JS, images
├── alembic/                 # Database migrations
├── requirements.txt
├── .env.example
└── README.md
```

## 🔧 Development

### Running Tests
```bash
pytest
```

### Code Formatting
```bash
black app/ scripts/ tests/
flake8 app/ scripts/ tests/
```

### Database Migrations
```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head
```

## 📊 Data Sources

- PoE 2 Trade API (when available)
- Community market data
- Historical pricing databases
- User-contributed modifier values

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
