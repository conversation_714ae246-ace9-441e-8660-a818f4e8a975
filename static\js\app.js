/**
 * Main JavaScript application for PoE 2 Tablet Optimizer
 */

// Global application state
const App = {
    config: {
        apiBaseUrl: '/api',
        refreshInterval: 300000, // 5 minutes
        chartColors: {
            primary: '#4169e1',
            success: '#32cd32',
            warning: '#ff8c00',
            danger: '#dc143c',
            gold: '#c9aa71'
        }
    },
    
    // Cache for API responses
    cache: new Map(),
    
    // Active intervals
    intervals: new Set(),
    
    // Initialize the application
    init() {
        this.setupGlobalErrorHandling();
        this.setupUtilityFunctions();
        this.startPeriodicUpdates();
        console.log('PoE 2 Tablet Optimizer initialized');
    },
    
    // Setup global error handling
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.showNotification('An unexpected error occurred', 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.showNotification('Network or server error', 'error');
        });
    },
    
    // Setup utility functions
    setupUtilityFunctions() {
        // Add copy to clipboard functionality
        this.setupClipboard();
        
        // Add tooltip initialization
        this.initializeTooltips();
        
        // Add form validation helpers
        this.setupFormValidation();
    },
    
    // Start periodic data updates
    startPeriodicUpdates() {
        // Clear existing intervals
        this.intervals.forEach(interval => clearInterval(interval));
        this.intervals.clear();
        
        // Add new intervals
        const marketUpdateInterval = setInterval(() => {
            this.refreshMarketData();
        }, this.config.refreshInterval);
        
        this.intervals.add(marketUpdateInterval);
    },
    
    // API helper methods
    async apiCall(endpoint, options = {}) {
        const url = `${this.config.apiBaseUrl}${endpoint}`;
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        
        // Check cache for GET requests
        if (!options.method || options.method === 'GET') {
            const cached = this.cache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < 60000) { // 1 minute cache
                return cached.data;
            }
        }
        
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // Cache successful GET requests
            if (!options.method || options.method === 'GET') {
                this.cache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                });
            }
            
            return data;
        } catch (error) {
            console.error(`API call failed for ${endpoint}:`, error);
            throw error;
        }
    },
    
    // Notification system
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },
    
    // Clipboard functionality
    setupClipboard() {
        document.addEventListener('click', async (event) => {
            if (event.target.classList.contains('copy-btn') || 
                event.target.closest('.copy-btn')) {
                
                const button = event.target.classList.contains('copy-btn') ? 
                    event.target : event.target.closest('.copy-btn');
                
                const textToCopy = button.dataset.copy || button.textContent;
                
                try {
                    await navigator.clipboard.writeText(textToCopy);
                    const originalText = button.textContent;
                    button.textContent = 'Copied!';
                    button.classList.add('btn-success');
                    
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.classList.remove('btn-success');
                    }, 2000);
                } catch (error) {
                    console.error('Failed to copy to clipboard:', error);
                    this.showNotification('Failed to copy to clipboard', 'error');
                }
            }
        });
    },
    
    // Initialize Bootstrap tooltips
    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // Form validation helpers
    setupFormValidation() {
        document.addEventListener('submit', (event) => {
            const form = event.target;
            if (form.classList.contains('needs-validation')) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }
        });
    },
    
    // Refresh market data
    async refreshMarketData() {
        try {
            // Clear relevant cache entries
            for (const [key] of this.cache) {
                if (key.includes('/market/') || key.includes('/modifiers/trending')) {
                    this.cache.delete(key);
                }
            }
            
            // Trigger refresh on current page if it has market data
            if (typeof loadDashboardData === 'function') {
                loadDashboardData();
            }
            
            console.log('Market data refreshed');
        } catch (error) {
            console.error('Failed to refresh market data:', error);
        }
    },
    
    // Utility functions
    formatCurrency(value, decimals = 2) {
        return `${value.toFixed(decimals)} ex`;
    },
    
    formatPercent(value, decimals = 1) {
        const sign = value >= 0 ? '+' : '';
        return `${sign}${value.toFixed(decimals)}%`;
    },
    
    formatNumber(value, decimals = 0) {
        return value.toLocaleString(undefined, {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },
    
    // Chart helpers
    createChart(canvasId, config) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            console.error(`Canvas element ${canvasId} not found`);
            return null;
        }
        
        // Destroy existing chart if it exists
        if (ctx.chart) {
            ctx.chart.destroy();
        }
        
        const chart = new Chart(ctx, config);
        ctx.chart = chart;
        return chart;
    },
    
    // Loading state management
    setLoading(element, isLoading = true) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        if (!element) return;
        
        if (isLoading) {
            element.classList.add('loading');
            element.style.pointerEvents = 'none';
        } else {
            element.classList.remove('loading');
            element.style.pointerEvents = '';
        }
    },
    
    // Cleanup on page unload
    cleanup() {
        this.intervals.forEach(interval => clearInterval(interval));
        this.intervals.clear();
        this.cache.clear();
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    App.cleanup();
});

// Enhanced functionality for Web Interface
App.enhancedFeatures = {
    // Quick action handlers
    setupQuickActions() {
        // Quick pattern generation
        window.generateQuickPattern = async function() {
            try {
                App.setLoading('generate-patterns', true);
                const patterns = await App.apiCall('/regex/generate', {
                    method: 'POST',
                    body: JSON.stringify({
                        tablet_type_id: null,
                        value_tier: 'high',
                        max_patterns: 3,
                        include_synergies: true
                    })
                });

                App.displayPatterns(patterns);
                App.showNotification('Quick patterns generated!', 'success');
            } catch (error) {
                App.showNotification('Failed to generate patterns', 'error');
            } finally {
                App.setLoading('generate-patterns', false);
            }
        };

        // Refresh market data
        window.refreshMarketData = async function() {
            try {
                App.showNotification('Refreshing market data...', 'info');
                const result = await App.apiCall('/market/refresh', { method: 'POST' });
                App.showNotification(`Updated ${result.updated_count} modifiers`, 'success');

                // Refresh current page data
                if (typeof loadMarketOverview === 'function') loadMarketOverview();
                if (typeof loadModifierReference === 'function') loadModifierReference();
            } catch (error) {
                App.showNotification('Failed to refresh market data', 'error');
            }
        };

        // Show trending items
        window.showTrendingItems = async function() {
            try {
                const trending = await App.apiCall('/modifiers/trending?direction=up&limit=10');
                App.displayTrendingModal(trending);
            } catch (error) {
                App.showNotification('Failed to load trending items', 'error');
            }
        };
    },

    // Modifier search functionality
    setupModifierSearch() {
        let searchTimeout;
        const searchInput = document.getElementById('modifier-search');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    App.enhancedFeatures.searchModifiers(this.value);
                }, 300);
            });
        }

        // Global search function
        window.searchModifiers = function() {
            const query = document.getElementById('modifier-search')?.value || '';
            App.enhancedFeatures.searchModifiers(query);
        };
    },

    // Search modifiers
    async searchModifiers(query) {
        try {
            const modifiers = await App.apiCall(`/modifiers/search?q=${encodeURIComponent(query)}`);
            App.enhancedFeatures.updateModifierTable(modifiers);
        } catch (error) {
            console.error('Search failed:', error);
        }
    },

    // Update modifier table
    updateModifierTable(modifiers) {
        const tbody = document.getElementById('modifier-table-body');
        if (!tbody) return;

        if (modifiers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No modifiers found</td></tr>';
            return;
        }

        tbody.innerHTML = modifiers.map(modifier => `
            <tr>
                <td>
                    <strong>${modifier.display_name}</strong><br>
                    <small class="text-muted">${modifier.description}</small>
                </td>
                <td><span class="badge bg-secondary">${modifier.category}</span></td>
                <td>${App.formatCurrency(modifier.current_value)}</td>
                <td class="${modifier.price_change_24h >= 0 ? 'text-success' : 'text-danger'}">
                    ${App.formatPercent(modifier.price_change_24h)}
                </td>
                <td>
                    <div class="trend-indicator ${App.enhancedFeatures.getTrendClass(modifier.trend_direction)}">
                        ${App.enhancedFeatures.getTrendIcon(modifier.trend_direction)}
                    </div>
                </td>
                <td>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar ${App.enhancedFeatures.getVolatilityClass(modifier.volatility)}"
                             style="width: ${modifier.volatility * 100}%"></div>
                    </div>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary copy-btn"
                            data-copy="${modifier.regex_pattern || modifier.name}">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info"
                            onclick="App.enhancedFeatures.showModifierDetails(${modifier.id})">
                        <i class="fas fa-info"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    },

    // Helper functions for table display
    getTrendClass(direction) {
        switch(direction) {
            case 'upward': return 'trend-up';
            case 'downward': return 'trend-down';
            default: return 'trend-neutral';
        }
    },

    getTrendIcon(direction) {
        switch(direction) {
            case 'upward': return '<i class="fas fa-arrow-up text-success"></i>';
            case 'downward': return '<i class="fas fa-arrow-down text-danger"></i>';
            default: return '<i class="fas fa-minus text-muted"></i>';
        }
    },

    getVolatilityClass(volatility) {
        if (volatility > 0.5) return 'bg-danger';
        if (volatility > 0.3) return 'bg-warning';
        return 'bg-success';
    },

    // Show modifier details modal
    async showModifierDetails(modifierId) {
        try {
            const modifier = await App.apiCall(`/modifiers/${modifierId}`);
            const history = await App.apiCall(`/market/price-history/${modifierId}?days=30`);

            // Create and show modal with detailed information
            App.enhancedFeatures.createModifierModal(modifier, history);
        } catch (error) {
            App.showNotification('Failed to load modifier details', 'error');
        }
    },

    // Create modifier details modal
    createModifierModal(modifier, history) {
        const modalHtml = `
            <div class="modal fade" id="modifierModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${modifier.display_name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Basic Information</h6>
                                    <p><strong>Category:</strong> ${modifier.category}</p>
                                    <p><strong>Current Value:</strong> ${App.formatCurrency(modifier.current_value)}</p>
                                    <p><strong>Base Value:</strong> ${App.formatCurrency(modifier.base_value)}</p>
                                    <p><strong>Is Valuable:</strong> ${modifier.is_valuable ? 'Yes' : 'No'}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Market Data</h6>
                                    <p><strong>24h Change:</strong>
                                        <span class="${modifier.price_change_24h >= 0 ? 'text-success' : 'text-danger'}">
                                            ${App.formatPercent(modifier.price_change_24h)}
                                        </span>
                                    </p>
                                    <p><strong>Volatility:</strong> ${(modifier.volatility * 100).toFixed(1)}%</p>
                                    <p><strong>Regex Pattern:</strong>
                                        <code class="copy-btn" data-copy="${modifier.regex_pattern}">${modifier.regex_pattern}</code>
                                    </p>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Price History (30 days)</h6>
                                    <canvas id="priceHistoryChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary copy-btn"
                                    data-copy="${modifier.regex_pattern}">Copy Regex</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal
        const existingModal = document.getElementById('modifierModal');
        if (existingModal) existingModal.remove();

        // Add new modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('modifierModal'));
        modal.show();

        // Create price history chart
        setTimeout(() => {
            App.enhancedFeatures.createPriceHistoryChart(history);
        }, 100);
    },

    // Create price history chart
    createPriceHistoryChart(history) {
        const ctx = document.getElementById('priceHistoryChart');
        if (!ctx || !history.length) return;

        const labels = history.map(h => new Date(h.timestamp).toLocaleDateString());
        const prices = history.map(h => h.price);

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Price (ex)',
                    data: prices,
                    borderColor: App.config.chartColors.primary,
                    backgroundColor: App.config.chartColors.primary + '20',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: false
                    }
                }
            }
        });
    }
};

// Initialize enhanced features
document.addEventListener('DOMContentLoaded', () => {
    App.enhancedFeatures.setupQuickActions();
    App.enhancedFeatures.setupModifierSearch();
});

// Export for global access
window.App = App;
