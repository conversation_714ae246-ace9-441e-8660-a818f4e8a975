/**
 * Real-time updates and WebSocket client for PoE 2 Tablet Optimizer
 */

const RealTime = {
    // WebSocket connection
    socket: null,
    connectionId: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectDelay: 1000,
    
    // Subscription management
    subscriptions: new Set(),
    alertHandlers: new Map(),
    
    // Connection state
    isConnected: false,
    lastPingTime: null,
    
    // Initialize real-time connection
    init() {
        this.connectionId = this.generateConnectionId();
        this.connect();
        this.setupHeartbeat();
        this.setupEventHandlers();
        console.log('Real-time service initialized');
    },
    
    // Generate unique connection ID
    generateConnectionId() {
        return 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },
    
    // Establish WebSocket connection
    connect() {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            return;
        }
        
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/${this.connectionId}`;
            
            this.socket = new WebSocket(wsUrl);
            this.setupSocketHandlers();
            
            console.log('Attempting WebSocket connection...');
        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.scheduleReconnect();
        }
    },
    
    // Setup WebSocket event handlers
    setupSocketHandlers() {
        this.socket.onopen = (event) => {
            console.log('WebSocket connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateConnectionStatus(true);
            
            // Subscribe to default alerts
            this.subscribeToAlerts(['price_spike', 'price_drop', 'market_condition']);
        };
        
        this.socket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
            }
        };
        
        this.socket.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            this.isConnected = false;
            this.updateConnectionStatus(false);
            
            if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            }
        };
        
        this.socket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.updateConnectionStatus(false);
        };
    },
    
    // Handle incoming WebSocket messages
    handleMessage(data) {
        const messageType = data.type;
        
        switch (messageType) {
            case 'connection_established':
                this.handleConnectionEstablished(data);
                break;
                
            case 'market_update':
                this.handleMarketUpdate(data);
                break;
                
            case 'ping':
                this.handlePing(data);
                break;
                
            case 'test_alert':
                this.handleTestAlert(data);
                break;
                
            default:
                console.log('Unknown message type:', messageType, data);
        }
    },
    
    // Handle connection established
    handleConnectionEstablished(data) {
        console.log('Connection established with server');
        
        if (data.market_stats) {
            this.updateMarketStats(data.market_stats);
        }
        
        // Show connection notification
        if (typeof App !== 'undefined') {
            App.showNotification('Real-time updates connected', 'success', 3000);
        }
    },
    
    // Handle market updates
    handleMarketUpdate(data) {
        console.log('Received market update:', data);
        
        // Update market statistics
        if (data.market_stats) {
            this.updateMarketStats(data.market_stats);
        }
        
        // Handle price alerts
        if (data.price_alerts && data.price_alerts.length > 0) {
            data.price_alerts.forEach(alert => {
                this.showPriceAlert(alert);
            });
        }
        
        // Handle market alert
        if (data.market_alert) {
            this.showMarketAlert(data.market_alert);
        }
        
        // Trigger custom handlers
        this.triggerAlertHandlers('market_update', data);
    },
    
    // Handle ping from server
    handlePing(data) {
        this.lastPingTime = new Date(data.timestamp);
        
        // Send pong response
        this.send({
            type: 'pong',
            timestamp: new Date().toISOString()
        });
    },
    
    // Handle test alerts
    handleTestAlert(data) {
        console.log('Received test alert:', data);
        
        if (typeof App !== 'undefined') {
            App.showNotification('Test alert received: ' + data.alert.message, 'info');
        }
    },
    
    // Update market statistics in UI
    updateMarketStats(stats) {
        // Update dashboard elements if they exist
        const elements = {
            'activeMarkets': stats.total_modifiers,
            'trendingUp': stats.trending_up,
            'trendingDown': stats.trending_down,
            'highVolatility': stats.high_volatility,
            'avgChange24h': stats.average_change_24h ? 
                (stats.average_change_24h >= 0 ? '+' : '') + stats.average_change_24h.toFixed(1) + '%' : '-'
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                
                // Add visual feedback for updates
                element.classList.add('updated');
                setTimeout(() => element.classList.remove('updated'), 1000);
            }
        });
        
        // Update last updated timestamp
        const lastUpdatedElement = document.getElementById('lastUpdated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = new Date().toLocaleTimeString();
        }
    },
    
    // Show price alert
    showPriceAlert(alert) {
        const alertClass = this.getAlertClass(alert.severity);
        const changeIcon = alert.price_change_percent >= 0 ? '📈' : '📉';
        
        const message = `${changeIcon} ${alert.modifier_name}: ${alert.price_change_percent >= 0 ? '+' : ''}${alert.price_change_percent.toFixed(1)}%`;
        
        if (typeof App !== 'undefined') {
            App.showNotification(message, alertClass, 8000);
        }
        
        // Add to alerts panel if it exists
        this.addToAlertsPanel(alert);
        
        // Play notification sound for high severity alerts
        if (alert.severity === 'high' || alert.severity === 'critical') {
            this.playNotificationSound();
        }
    },
    
    // Show market alert
    showMarketAlert(alert) {
        const alertClass = this.getAlertClass(alert.severity);
        
        if (typeof App !== 'undefined') {
            App.showNotification(`Market Alert: ${alert.message}`, alertClass, 10000);
        }
        
        // Update market sentiment indicator
        this.updateMarketSentiment(alert.condition);
    },
    
    // Add alert to alerts panel
    addToAlertsPanel(alert) {
        const alertsContainer = document.getElementById('marketAlerts');
        if (!alertsContainer) return;
        
        const alertElement = document.createElement('div');
        alertElement.className = `alert alert-${this.getAlertClass(alert.severity)} alert-dismissible fade show mb-2`;
        alertElement.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <strong>${alert.modifier_name}</strong><br>
                    <small>${alert.message}</small><br>
                    <small class="text-muted">${new Date(alert.timestamp).toLocaleTimeString()}</small>
                </div>
                <div class="text-end">
                    <div class="fw-bold">${alert.price_change_percent >= 0 ? '+' : ''}${alert.price_change_percent.toFixed(1)}%</div>
                    <small>${alert.current_price.toFixed(2)} ex</small>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Add to top of alerts container
        alertsContainer.insertBefore(alertElement, alertsContainer.firstChild);
        
        // Remove old alerts (keep only last 10)
        const alerts = alertsContainer.querySelectorAll('.alert');
        if (alerts.length > 10) {
            for (let i = 10; i < alerts.length; i++) {
                alerts[i].remove();
            }
        }
    },
    
    // Update market sentiment indicator
    updateMarketSentiment(condition) {
        const sentimentElement = document.getElementById('marketSentiment');
        if (!sentimentElement) return;
        
        const sentiments = {
            'bullish': { text: 'Bullish 🐂', class: 'text-success' },
            'bearish': { text: 'Bearish 🐻', class: 'text-danger' },
            'volatile': { text: 'Volatile ⚡', class: 'text-warning' },
            'stable': { text: 'Stable 📊', class: 'text-info' }
        };
        
        const sentiment = sentiments[condition] || { text: condition, class: 'text-muted' };
        
        sentimentElement.textContent = sentiment.text;
        sentimentElement.className = `stat-value ${sentiment.class}`;
    },
    
    // Get Bootstrap alert class from severity
    getAlertClass(severity) {
        const classes = {
            'low': 'info',
            'medium': 'warning',
            'high': 'danger',
            'critical': 'danger'
        };
        return classes[severity] || 'info';
    },
    
    // Play notification sound
    playNotificationSound() {
        try {
            // Create audio context for notification sound
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        } catch (error) {
            console.log('Could not play notification sound:', error);
        }
    },
    
    // Subscribe to specific alert types
    subscribeToAlerts(alertTypes) {
        alertTypes.forEach(type => this.subscriptions.add(type));
        
        this.send({
            type: 'subscribe_alerts',
            alert_types: Array.from(this.subscriptions)
        });
    },
    
    // Add custom alert handler
    addAlertHandler(eventType, handler) {
        if (!this.alertHandlers.has(eventType)) {
            this.alertHandlers.set(eventType, []);
        }
        this.alertHandlers.get(eventType).push(handler);
    },
    
    // Trigger custom alert handlers
    triggerAlertHandlers(eventType, data) {
        const handlers = this.alertHandlers.get(eventType);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error('Error in alert handler:', error);
                }
            });
        }
    },
    
    // Send message to server
    send(data) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(data));
        } else {
            console.warn('Cannot send message: WebSocket not connected');
        }
    },
    
    // Request manual update
    requestUpdate() {
        this.send({
            type: 'request_update',
            timestamp: new Date().toISOString()
        });
    },
    
    // Setup heartbeat to keep connection alive
    setupHeartbeat() {
        setInterval(() => {
            if (this.isConnected && this.lastPingTime) {
                const timeSinceLastPing = Date.now() - this.lastPingTime.getTime();
                
                // If no ping received in 2 minutes, consider connection stale
                if (timeSinceLastPing > 120000) {
                    console.warn('No ping received for 2 minutes, reconnecting...');
                    this.reconnect();
                }
            }
        }, 30000); // Check every 30 seconds
    },
    
    // Setup UI event handlers
    setupEventHandlers() {
        // Add refresh button handler if it exists
        const refreshButton = document.getElementById('refreshMarketData');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.requestUpdate();
            });
        }
        
        // Add connection status click handler
        document.addEventListener('click', (event) => {
            if (event.target.id === 'connectionStatus') {
                if (!this.isConnected) {
                    this.reconnect();
                }
            }
        });
    },
    
    // Update connection status indicator
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            if (connected) {
                statusElement.innerHTML = '<i class="fas fa-circle text-success"></i> Connected';
                statusElement.title = 'Real-time updates active';
            } else {
                statusElement.innerHTML = '<i class="fas fa-circle text-danger"></i> Disconnected';
                statusElement.title = 'Click to reconnect';
                statusElement.style.cursor = 'pointer';
            }
        }
    },
    
    // Schedule reconnection attempt
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
        
        console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            this.connect();
        }, delay);
    },
    
    // Manual reconnection
    reconnect() {
        if (this.socket) {
            this.socket.close();
        }
        this.reconnectAttempts = 0;
        this.connect();
    },
    
    // Cleanup
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        this.isConnected = false;
        this.updateConnectionStatus(false);
    }
};

// Global functions for HTML onclick handlers
window.requestMarketUpdate = () => RealTime.requestUpdate();
window.toggleRealTimeUpdates = function() {
    if (RealTime.isConnected) {
        RealTime.disconnect();
    } else {
        RealTime.reconnect();
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if WebSocket is supported
    if (typeof WebSocket !== 'undefined') {
        RealTime.init();
    } else {
        console.warn('WebSocket not supported by this browser');
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    RealTime.disconnect();
});

// Export for global access
window.RealTime = RealTime;
