"""
Market data and pricing models.
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, ForeignKey, DateTime, Float, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class MarketData(Base):
    """
    Real-time market data for modifiers and tablets.
    Tracks current pricing and availability.
    """
    __tablename__ = "market_data"
    
    id = Column(Integer, primary_key=True, index=True)
    modifier_id = Column(Integer, ForeignKey("modifiers.id"), nullable=False)
    
    # Current market metrics
    current_price = Column(Float, nullable=False)  # Current market price
    price_change_24h = Column(Float, default=0.0)  # 24h price change
    price_change_7d = Column(Float, default=0.0)  # 7d price change
    
    # Market activity
    volume_24h = Column(Integer, default=0)  # Trading volume last 24h
    listings_count = Column(Integer, default=0)  # Current active listings
    avg_listing_time = Column(Float, default=0.0)  # Average time to sell (hours)
    
    # Price statistics
    price_min = Column(Float)  # Minimum price in dataset
    price_max = Column(Float)  # Maximum price in dataset
    price_median = Column(Float)  # Median price
    price_std_dev = Column(Float)  # Price standard deviation
    
    # Market indicators
    liquidity_score = Column(Float, default=0.0)  # How liquid the market is (0-1)
    volatility_score = Column(Float, default=0.0)  # Price volatility (0-1)
    trend_strength = Column(Float, default=0.0)  # Strength of current trend (0-1)
    
    # Data quality
    data_quality = Column(Float, default=0.0)  # Quality of data (0-1)
    last_updated = Column(DateTime(timezone=True), server_default=func.now())
    update_frequency = Column(Integer, default=6)  # Hours between updates
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    modifier = relationship("Modifier", back_populates="market_data")
    price_history = relationship("PriceHistory", back_populates="market_data")
    
    def __repr__(self):
        return f"<MarketData(modifier='{self.modifier.name}', price={self.current_price})>"
    
    @property
    def is_trending_up(self) -> bool:
        """Check if price is trending upward."""
        return self.price_change_24h > 0 and self.trend_strength > 0.5
    
    @property
    def is_volatile(self) -> bool:
        """Check if market is volatile."""
        from app.config import data_config
        return self.volatility_score > data_config.VOLATILITY_THRESHOLD


class PriceHistory(Base):
    """
    Historical price data for trend analysis and predictions.
    Stores time-series data for machine learning models.
    """
    __tablename__ = "price_history"
    
    id = Column(Integer, primary_key=True, index=True)
    market_data_id = Column(Integer, ForeignKey("market_data.id"), nullable=False)
    
    # Price data
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    price = Column(Float, nullable=False)
    volume = Column(Integer, default=0)
    
    # Market context
    listings_count = Column(Integer, default=0)
    avg_listing_price = Column(Float)
    market_cap = Column(Float)  # Total value of all listings
    
    # Technical indicators
    moving_avg_7d = Column(Float)  # 7-day moving average
    moving_avg_30d = Column(Float)  # 30-day moving average
    rsi = Column(Float)  # Relative Strength Index
    bollinger_upper = Column(Float)  # Bollinger band upper
    bollinger_lower = Column(Float)  # Bollinger band lower
    
    # External factors
    league_day = Column(Integer)  # Days since league start
    patch_version = Column(String(20))  # Game patch version
    meta_score = Column(Float)  # How meta-relevant the modifier is
    
    # Metadata
    data_source = Column(String(100))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    market_data = relationship("MarketData", back_populates="price_history")
    
    def __repr__(self):
        return f"<PriceHistory(timestamp={self.timestamp}, price={self.price})>"


class MarketTrend(Base):
    """
    Analyzed market trends and patterns.
    Results from trend analysis algorithms.
    """
    __tablename__ = "market_trends"
    
    id = Column(Integer, primary_key=True, index=True)
    modifier_id = Column(Integer, ForeignKey("modifiers.id"), nullable=False)
    
    # Trend analysis
    trend_type = Column(String(50), nullable=False)  # "bullish", "bearish", "sideways"
    trend_strength = Column(Float, default=0.0)  # Strength of trend (0-1)
    trend_duration = Column(Integer, default=0)  # Days trend has been active
    
    # Predictions
    predicted_price_1d = Column(Float)  # 1-day price prediction
    predicted_price_7d = Column(Float)  # 7-day price prediction
    predicted_price_30d = Column(Float)  # 30-day price prediction
    prediction_confidence = Column(Float, default=0.0)  # Confidence in predictions
    
    # Pattern recognition
    pattern_type = Column(String(100))  # Detected chart pattern
    support_level = Column(Float)  # Support price level
    resistance_level = Column(Float)  # Resistance price level
    breakout_probability = Column(Float, default=0.0)  # Probability of breakout
    
    # Risk metrics
    volatility_forecast = Column(Float, default=0.0)  # Expected volatility
    downside_risk = Column(Float, default=0.0)  # Potential downside risk
    upside_potential = Column(Float, default=0.0)  # Potential upside
    
    # Analysis metadata
    analysis_date = Column(DateTime(timezone=True), server_default=func.now())
    model_version = Column(String(50))  # Version of analysis model used
    data_points_used = Column(Integer)  # Number of data points in analysis
    
    # Additional insights
    market_sentiment = Column(String(50))  # "bullish", "bearish", "neutral"
    seasonal_factor = Column(Float, default=1.0)  # Seasonal adjustment factor
    correlation_factors = Column(JSON)  # Factors correlated with price movement
    
    # Relationships
    modifier = relationship("Modifier")
    
    def __repr__(self):
        return f"<MarketTrend(modifier='{self.modifier.name}', trend='{self.trend_type}')>"
    
    @property
    def is_bullish(self) -> bool:
        """Check if trend is bullish."""
        return self.trend_type == "bullish" and self.trend_strength > 0.6
    
    @property
    def has_high_confidence(self) -> bool:
        """Check if predictions have high confidence."""
        from app.config import data_config
        return self.prediction_confidence > data_config.CONFIDENCE_THRESHOLD
