# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/poe_tablet_optimizer
# For development with SQLite:
# DATABASE_URL=sqlite:///./poe_tablet_optimizer.db

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
SECRET_KEY=your-secret-key-here

# PoE 2 Trade API (when available)
POE2_TRADE_API_URL=https://api.pathofexile2.com/trade
POE2_API_KEY=your-api-key-here

# Data Collection Settings
DATA_UPDATE_INTERVAL_HOURS=6
MARKET_DATA_RETENTION_DAYS=365
ENABLE_PREDICTIVE_ANALYTICS=True

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# External Services
BACKUP_STORAGE_URL=
NOTIFICATION_WEBHOOK_URL=
