"""
Service layer for market data operations and PoE API integration.
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import httpx
import asyncio
import json
import logging

from app.models.market import MarketData, PriceHistory, MarketTrend
from app.models.modifier import Modifier
from app.schemas.market import MarketDataResponse, PriceHistoryResponse, MarketOverview, PriceAlert, ArbitrageOpportunity
from app.config import settings

logger = logging.getLogger(__name__)


class PoEAPIClient:
    """Client for interacting with Path of Exile trade APIs."""
    
    def __init__(self, poesessid: str = "8646be150d5771916c09c68896a6bc9d"):
        self.poesessid = poesessid
        self.base_url = "https://pathofexile.com/api/trade2"
        self.headers = {
            "User-Agent": "PoE2TabletOptimizer/1.0",
            "Cookie": f"POESESSID={poesessid}",
            "Accept": "application/json"
        }
        self.rate_limit_delay = 1.0  # Seconds between requests
        self.last_request_time = 0
    
    async def _make_request(self, url: str) -> Dict[Any, Any]:
        """Make a rate-limited request to the PoE API."""
        # Rate limiting
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, headers=self.headers, timeout=30.0)
                self.last_request_time = asyncio.get_event_loop().time()
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:
                    # Rate limited, wait longer
                    logger.warning("Rate limited by PoE API, waiting 5 seconds")
                    await asyncio.sleep(5)
                    return await self._make_request(url)
                else:
                    logger.error(f"PoE API request failed: {response.status_code} - {response.text}")
                    return {}
            except Exception as e:
                logger.error(f"Error making PoE API request to {url}: {e}")
                return {}
    
    async def get_leagues(self) -> List[Dict[str, Any]]:
        """Get available leagues."""
        url = f"{self.base_url}/data/leagues"
        data = await self._make_request(url)
        return data.get("result", [])
    
    async def get_filters(self) -> Dict[str, Any]:
        """Get available trade filters."""
        url = f"{self.base_url}/data/filters"
        return await self._make_request(url)
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get available stats/modifiers."""
        url = f"{self.base_url}/data/stats"
        return await self._make_request(url)
    
    async def get_static_data(self) -> Dict[str, Any]:
        """Get static game data."""
        url = f"{self.base_url}/data/static"
        return await self._make_request(url)
    
    async def get_items(self) -> Dict[str, Any]:
        """Get item data."""
        url = f"{self.base_url}/data/items"
        return await self._make_request(url)
    
    async def search_items(self, query: Dict[str, Any], league: str = "Standard") -> Dict[str, Any]:
        """Search for items with specific criteria."""
        url = f"{self.base_url}/search/{league}"
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    url, 
                    headers=self.headers, 
                    json=query,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"Search request failed: {response.status_code} - {response.text}")
                    return {}
            except Exception as e:
                logger.error(f"Error searching items: {e}")
                return {}
    
    async def fetch_items(self, item_ids: List[str], query_id: str, league: str = "Standard") -> Dict[str, Any]:
        """Fetch specific items by their IDs."""
        if not item_ids:
            return {}
        
        # Limit to 10 items per request (PoE API limitation)
        item_ids = item_ids[:10]
        ids_str = ",".join(item_ids)
        url = f"{self.base_url}/fetch/{ids_str}?query={query_id}"
        
        return await self._make_request(url)


class MarketService:
    """Service for market data operations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.poe_client = PoEAPIClient()
    
    async def get_market_data(self, modifier_ids: Optional[List[int]] = None,
                            tablet_type_id: Optional[int] = None,
                            min_price: Optional[float] = None,
                            max_price: Optional[float] = None,
                            trending_only: bool = False,
                            limit: int = 50) -> List[MarketDataResponse]:
        """Get current market data with optional filtering."""
        query = self.db.query(MarketData)
        
        if modifier_ids:
            query = query.filter(MarketData.modifier_id.in_(modifier_ids))
        
        if tablet_type_id:
            query = query.join(Modifier).filter(Modifier.tablet_type_id == tablet_type_id)
        
        if min_price is not None:
            query = query.filter(MarketData.current_price >= min_price)
        
        if max_price is not None:
            query = query.filter(MarketData.current_price <= max_price)
        
        if trending_only:
            query = query.filter(MarketData.trend_strength > 0.5)
        
        market_data = query.order_by(desc(MarketData.last_updated)).limit(limit).all()
        return market_data
    
    async def get_price_history(self, modifier_id: int, days: int = 30, 
                              interval: str = "daily") -> List[PriceHistoryResponse]:
        """Get price history for a modifier."""
        since_date = datetime.utcnow() - timedelta(days=days)
        
        query = self.db.query(PriceHistory).join(MarketData).filter(
            and_(
                MarketData.modifier_id == modifier_id,
                PriceHistory.timestamp >= since_date
            )
        )
        
        # Apply interval filtering
        if interval == "hourly":
            # Return all data points
            pass
        elif interval == "daily":
            # Group by day (simplified - would need more complex aggregation)
            query = query.filter(func.extract('hour', PriceHistory.timestamp) == 12)
        elif interval == "weekly":
            # Group by week
            query = query.filter(func.extract('dow', PriceHistory.timestamp) == 1)
        
        history = query.order_by(PriceHistory.timestamp.desc()).all()
        return history
    
    async def get_market_overview(self, tablet_type_id: Optional[int] = None) -> MarketOverview:
        """Get market overview data for dashboard."""
        query = self.db.query(MarketData)
        
        if tablet_type_id:
            query = query.join(Modifier).filter(Modifier.tablet_type_id == tablet_type_id)
        
        market_data = query.all()
        
        if not market_data:
            return MarketOverview(
                total_modifiers_tracked=0,
                active_markets=0,
                trending_up_count=0,
                trending_down_count=0,
                high_volatility_count=0,
                average_price_change_24h=0.0,
                average_price_change_7d=0.0,
                highest_price_change_24h=0.0,
                lowest_price_change_24h=0.0,
                total_volume_24h=0,
                average_volume_24h=0.0,
                overall_market_sentiment="neutral",
                market_volatility_index=0.0,
                liquidity_index=0.0,
                significant_price_changes=[],
                new_trending_modifiers=[],
                last_updated=datetime.utcnow()
            )
        
        # Calculate overview statistics
        total_modifiers = len(market_data)
        active_markets = sum(1 for md in market_data if md.listings_count > 0)
        trending_up = sum(1 for md in market_data if md.price_change_24h > 5)
        trending_down = sum(1 for md in market_data if md.price_change_24h < -5)
        high_volatility = sum(1 for md in market_data if md.volatility_score > 0.3)
        
        price_changes_24h = [md.price_change_24h for md in market_data]
        price_changes_7d = [md.price_change_7d for md in market_data]
        volumes_24h = [md.volume_24h for md in market_data]
        
        avg_price_change_24h = sum(price_changes_24h) / len(price_changes_24h) if price_changes_24h else 0
        avg_price_change_7d = sum(price_changes_7d) / len(price_changes_7d) if price_changes_7d else 0
        highest_change = max(price_changes_24h) if price_changes_24h else 0
        lowest_change = min(price_changes_24h) if price_changes_24h else 0
        
        total_volume = sum(volumes_24h)
        avg_volume = total_volume / len(volumes_24h) if volumes_24h else 0
        
        # Market sentiment calculation
        if avg_price_change_24h > 2:
            sentiment = "bullish"
        elif avg_price_change_24h < -2:
            sentiment = "bearish"
        else:
            sentiment = "neutral"
        
        # Market indices
        volatility_scores = [md.volatility_score for md in market_data]
        liquidity_scores = [md.liquidity_score for md in market_data]
        
        volatility_index = sum(volatility_scores) / len(volatility_scores) if volatility_scores else 0
        liquidity_index = sum(liquidity_scores) / len(liquidity_scores) if liquidity_scores else 0
        
        return MarketOverview(
            total_modifiers_tracked=total_modifiers,
            active_markets=active_markets,
            trending_up_count=trending_up,
            trending_down_count=trending_down,
            high_volatility_count=high_volatility,
            average_price_change_24h=avg_price_change_24h,
            average_price_change_7d=avg_price_change_7d,
            highest_price_change_24h=highest_change,
            lowest_price_change_24h=lowest_change,
            total_volume_24h=total_volume,
            average_volume_24h=avg_volume,
            overall_market_sentiment=sentiment,
            market_volatility_index=volatility_index,
            liquidity_index=liquidity_index,
            significant_price_changes=[],  # Would be populated with actual alerts
            new_trending_modifiers=[],     # Would be populated with trending data
            last_updated=datetime.utcnow()
        )
    
    async def refresh_market_data(self, modifier_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """Trigger manual refresh of market data from PoE APIs."""
        try:
            # Get current league
            leagues = await self.poe_client.get_leagues()
            current_league = None
            for league in leagues:
                if not league.get("event", False):  # Not an event league
                    current_league = league["id"]
                    break
            
            if not current_league:
                current_league = "Standard"  # Fallback
            
            logger.info(f"Refreshing market data for league: {current_league}")
            
            # Get modifiers to update
            query = self.db.query(Modifier).filter(Modifier.is_active == True)
            if modifier_ids:
                query = query.filter(Modifier.id.in_(modifier_ids))
            
            modifiers = query.all()
            updated_count = 0
            
            for modifier in modifiers:
                try:
                    # Create search query for this modifier
                    search_query = await self._create_search_query_for_modifier(modifier)
                    
                    if search_query:
                        # Search for items with this modifier
                        search_result = await self.poe_client.search_items(search_query, current_league)
                        
                        if search_result.get("result"):
                            # Process search results and update market data
                            await self._process_search_results(modifier, search_result, current_league)
                            updated_count += 1
                    
                    # Rate limiting
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"Error updating market data for modifier {modifier.id}: {e}")
                    continue
            
            return {"updated_count": updated_count, "league": current_league}
            
        except Exception as e:
            logger.error(f"Error refreshing market data: {e}")
            return {"updated_count": 0, "error": str(e)}
    
    async def _create_search_query_for_modifier(self, modifier: Modifier) -> Optional[Dict[str, Any]]:
        """Create a search query for a specific modifier."""
        # This would create PoE API search queries based on modifier data
        # For now, return a basic structure
        return {
            "query": {
                "status": {"option": "online"},
                "type": {"option": "any"},
                "stats": [{
                    "type": "and",
                    "filters": []
                }]
            },
            "sort": {
                "price": "asc"
            }
        }
    
    async def _process_search_results(self, modifier: Modifier, search_result: Dict[str, Any], league: str):
        """Process search results and update market data."""
        # This would process the actual search results and update market data
        # For now, create placeholder market data
        
        # Check if market data exists for this modifier
        market_data = self.db.query(MarketData).filter(
            MarketData.modifier_id == modifier.id
        ).first()
        
        if not market_data:
            market_data = MarketData(modifier_id=modifier.id)
            self.db.add(market_data)
        
        # Update with placeholder data (would be real data from API)
        market_data.current_price = modifier.base_value * (1 + (hash(modifier.name) % 20 - 10) / 100)
        market_data.price_change_24h = (hash(modifier.name) % 20 - 10) / 2
        market_data.volume_24h = hash(modifier.name) % 100 + 10
        market_data.listings_count = hash(modifier.name) % 50 + 5
        market_data.volatility_score = (hash(modifier.name) % 30) / 100
        market_data.liquidity_score = 0.7
        market_data.last_updated = datetime.utcnow()
        
        self.db.commit()
        logger.info(f"Updated market data for modifier: {modifier.name}")
    
    async def get_price_alerts(self, threshold_percent: float = 10.0, hours: int = 24) -> List[PriceAlert]:
        """Get significant price changes in the specified time period."""
        since_time = datetime.utcnow() - timedelta(hours=hours)
        
        # Get market data with significant price changes
        market_data = self.db.query(MarketData).filter(
            and_(
                MarketData.last_updated >= since_time,
                or_(
                    MarketData.price_change_24h >= threshold_percent,
                    MarketData.price_change_24h <= -threshold_percent
                )
            )
        ).all()
        
        alerts = []
        for md in market_data:
            alert_type = "spike" if md.price_change_24h > 0 else "drop"
            severity = "critical" if abs(md.price_change_24h) > 25 else "high" if abs(md.price_change_24h) > 15 else "medium"
            
            alert = PriceAlert(
                modifier_id=md.modifier_id,
                modifier_name=md.modifier.name if md.modifier else "Unknown",
                old_price=md.current_price / (1 + md.price_change_24h / 100),
                new_price=md.current_price,
                price_change_percent=md.price_change_24h,
                volume_change_percent=0.0,  # Would calculate from historical data
                alert_timestamp=md.last_updated,
                alert_type=alert_type,
                severity=severity
            )
            alerts.append(alert)
        
        return alerts
