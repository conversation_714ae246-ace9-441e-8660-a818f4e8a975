"""
Service layer for tablet operations.
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.models.tablet import Tablet, TabletType
from app.models.modifier import Modifier
from app.schemas.tablet import TabletCreate, TabletSummaryStats
from app.services.valuation_service import ValuationEngine
from app.config import data_config


class TabletService:
    """Service for tablet-related business logic."""
    
    def __init__(self, db: Session):
        self.db = db
        self.valuation_engine = ValuationEngine(db)
    
    async def evaluate_tablet(self, tablet_data: TabletCreate) -> Tablet:
        """
        Evaluate a tablet configuration and calculate its market value.
        This is the core functionality for tablet evaluation.
        """
        # Get tablet type
        tablet_type = self.db.query(TabletType).filter(
            TabletType.id == tablet_data.tablet_type_id
        ).first()
        
        if not tablet_type:
            raise ValueError("Invalid tablet type ID")
        
        # Get modifiers
        modifier1 = None
        modifier2 = None
        
        if tablet_data.modifier1_id:
            modifier1 = self.db.query(Modifier).filter(
                and_(
                    Modifier.id == tablet_data.modifier1_id,
                    Modifier.tablet_type_id == tablet_data.tablet_type_id
                )
            ).first()
            
            if not modifier1:
                raise ValueError("Invalid modifier1 ID for this tablet type")
        
        if tablet_data.modifier2_id:
            modifier2 = self.db.query(Modifier).filter(
                and_(
                    Modifier.id == tablet_data.modifier2_id,
                    Modifier.tablet_type_id == tablet_data.tablet_type_id
                )
            ).first()
            
            if not modifier2:
                raise ValueError("Invalid modifier2 ID for this tablet type")
        
        # Create tablet instance
        tablet = Tablet(
            tablet_type_id=tablet_data.tablet_type_id,
            modifier1_id=tablet_data.modifier1_id,
            modifier1_tier=tablet_data.modifier1_tier,
            modifier2_id=tablet_data.modifier2_id,
            modifier2_tier=tablet_data.modifier2_tier,
            user_notes=tablet_data.user_notes
        )
        
        # Calculate values using advanced valuation engine
        valuation = self.valuation_engine.calculate_tablet_value(
            tablet_type, modifier1, modifier2,
            tablet_data.modifier1_tier, tablet_data.modifier2_tier
        )

        # Update tablet with valuation results
        tablet.base_value = valuation["modifier1_value"] + valuation["modifier2_value"]
        tablet.synergy_bonus = valuation["synergy_bonus"]
        tablet.total_value = valuation["total_value"]
        tablet.profit_estimate = valuation["profit_estimate"]
        tablet.market_confidence = valuation["confidence_score"]
        tablet.last_market_check = datetime.utcnow()
        
        # Save to database
        self.db.add(tablet)
        self.db.commit()
        self.db.refresh(tablet)
        
        return tablet
    
    async def _calculate_tablet_value(self, tablet: Tablet, modifier1: Optional[Modifier], modifier2: Optional[Modifier]):
        """Calculate the total value of a tablet based on its modifiers."""
        base_value = 0.0
        synergy_bonus = 0.0
        
        # Calculate base value from individual modifiers
        if modifier1:
            mod1_value = modifier1.current_value
            # Apply tier multiplier
            tier_multiplier = self._get_tier_multiplier(modifier1, tablet.modifier1_tier)
            base_value += mod1_value * tier_multiplier
        
        if modifier2:
            mod2_value = modifier2.current_value
            # Apply tier multiplier
            tier_multiplier = self._get_tier_multiplier(modifier2, tablet.modifier2_tier)
            base_value += mod2_value * tier_multiplier
        
        # Calculate synergy bonus if both modifiers present
        if modifier1 and modifier2:
            synergy_bonus = await self._calculate_synergy_bonus(modifier1, modifier2)
        
        # Calculate total value and profit
        total_value = base_value + synergy_bonus
        total_cost = tablet.tablet_type.base_cost + tablet.tablet_type.crafting_cost
        profit_estimate = total_value - total_cost
        
        # Calculate market confidence (simplified)
        confidence = 0.8  # Default confidence, would be based on market data quality
        if modifier1 and hasattr(modifier1, 'modifier_values') and modifier1.modifier_values:
            latest_value = max(modifier1.modifier_values, key=lambda x: x.created_at)
            confidence = min(confidence, latest_value.confidence)
        
        # Update tablet values
        tablet.base_value = base_value
        tablet.synergy_bonus = synergy_bonus
        tablet.total_value = total_value
        tablet.profit_estimate = profit_estimate
        tablet.market_confidence = confidence
        tablet.last_market_check = datetime.utcnow()
    
    def _get_tier_multiplier(self, modifier: Modifier, tier: int) -> float:
        """Get the value multiplier for a specific modifier tier."""
        # Simple tier multiplier calculation
        # In a real implementation, this would use ModifierTier data
        if tier <= 0:
            return 1.0
        
        # Higher tiers are worth more
        base_multiplier = 1.0
        tier_bonus = (tier - 1) * 0.2  # 20% bonus per tier above 1
        return base_multiplier + tier_bonus
    
    async def _calculate_synergy_bonus(self, modifier1: Modifier, modifier2: Modifier) -> float:
        """Calculate synergy bonus between two modifiers."""
        # Simplified synergy calculation
        # In a real implementation, this would use a synergy database
        
        # Same category bonus
        if modifier1.category == modifier2.category:
            return (modifier1.current_value + modifier2.current_value) * 0.1
        
        # Complementary category bonuses
        synergy_pairs = {
            ('damage', 'utility'): 0.15,
            ('defense', 'resource'): 0.12,
            ('damage', 'resource'): 0.08,
        }
        
        pair_key = (modifier1.category.value, modifier2.category.value)
        reverse_pair_key = (modifier2.category.value, modifier1.category.value)
        
        multiplier = synergy_pairs.get(pair_key, synergy_pairs.get(reverse_pair_key, 0.05))
        return (modifier1.current_value + modifier2.current_value) * multiplier
    
    async def mark_sold(self, tablet: Tablet, sale_price: float):
        """Mark a tablet as sold with the actual sale price."""
        tablet.is_sold = True
        tablet.actual_sale_price = sale_price
        tablet.sale_date = datetime.utcnow()
        
        self.db.commit()
        
        # Update modifier value data based on actual sale
        # This helps improve future valuations
        await self._update_modifier_values_from_sale(tablet, sale_price)
    
    async def _update_modifier_values_from_sale(self, tablet: Tablet, sale_price: float):
        """Update modifier value estimates based on actual sale data."""
        # This would implement machine learning to improve valuations
        # For now, just log the sale for future analysis
        pass
    
    async def get_summary_stats(self, tablet_type_id: Optional[int] = None, days: int = 30) -> TabletSummaryStats:
        """Get summary statistics for tablets."""
        query = self.db.query(Tablet)
        
        if tablet_type_id:
            query = query.filter(Tablet.tablet_type_id == tablet_type_id)
        
        # Filter by date range
        since_date = datetime.utcnow() - timedelta(days=days)
        query = query.filter(Tablet.created_at >= since_date)
        
        tablets = query.all()
        
        if not tablets:
            return TabletSummaryStats(
                total_tablets=0,
                profitable_tablets=0,
                average_value=0.0,
                average_profit=0.0,
                total_profit=0.0,
                success_rate=0.0,
                high_value_count=0,
                medium_value_count=0,
                low_value_count=0,
                tablets_last_24h=0,
                profit_last_24h=0.0,
                best_tablet_value=0.0,
                worst_tablet_value=0.0
            )
        
        # Calculate statistics
        total_tablets = len(tablets)
        profitable_tablets = sum(1 for t in tablets if t.is_profitable)
        average_value = sum(t.total_value for t in tablets) / total_tablets
        average_profit = sum(t.profit_estimate for t in tablets) / total_tablets
        total_profit = sum(t.profit_estimate for t in tablets if t.is_profitable)
        success_rate = (profitable_tablets / total_tablets) * 100
        
        # Value tier counts
        high_value_count = sum(1 for t in tablets if t.value_tier == "high")
        medium_value_count = sum(1 for t in tablets if t.value_tier == "medium")
        low_value_count = sum(1 for t in tablets if t.value_tier == "low")
        
        # Recent activity
        last_24h = datetime.utcnow() - timedelta(hours=24)
        recent_tablets = [t for t in tablets if t.created_at >= last_24h]
        tablets_last_24h = len(recent_tablets)
        profit_last_24h = sum(t.profit_estimate for t in recent_tablets if t.is_profitable)
        
        # Best/worst
        best_tablet_value = max(t.total_value for t in tablets)
        worst_tablet_value = min(t.total_value for t in tablets)
        
        return TabletSummaryStats(
            total_tablets=total_tablets,
            profitable_tablets=profitable_tablets,
            average_value=average_value,
            average_profit=average_profit,
            total_profit=total_profit,
            success_rate=success_rate,
            high_value_count=high_value_count,
            medium_value_count=medium_value_count,
            low_value_count=low_value_count,
            tablets_last_24h=tablets_last_24h,
            profit_last_24h=profit_last_24h,
            best_tablet_value=best_tablet_value,
            worst_tablet_value=worst_tablet_value
        )
