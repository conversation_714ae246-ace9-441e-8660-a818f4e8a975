# Deployment Guide

This guide covers deploying the PoE 2 Tablet Crafting Profit Optimizer to production environments.

## 🚀 Production Deployment

### Prerequisites

- Ubuntu 20.04+ or similar Linux distribution
- Docker and Docker Compose
- Domain name with SSL certificate
- PostgreSQL database (local or cloud)
- Minimum 2GB RAM, 2 CPU cores

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install nginx
sudo apt install nginx -y
```

### 2. Application Setup

```bash
# Clone repository
git clone https://github.com/your-username/poe2-tablet-optimizer.git
cd poe2-tablet-optimizer

# Create production environment file
cp .env.example .env.production
```

Edit `.env.production`:
```env
ENVIRONMENT=production
DEBUG=False
DATABASE_URL=postgresql://username:password@localhost:5432/poe2_optimizer
POE_SESSION_ID=your_production_poesessid
SECRET_KEY=your_very_secure_secret_key
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

### 3. Database Setup

```bash
# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Create database and user
sudo -u postgres psql
```

```sql
CREATE DATABASE poe2_optimizer;
CREATE USER poe2_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE poe2_optimizer TO poe2_user;
\q
```

### 4. Docker Deployment

Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .env.production
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    command: gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./static:/var/www/static
    depends_on:
      - web
    restart: unless-stopped
```

### 5. Nginx Configuration

Create `nginx.conf`:
```nginx
events {
    worker_connections 1024;
}

http {
    upstream app {
        server web:8000;
    }

    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /ws/ {
            proxy_pass http://app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### 6. SSL Certificate Setup

```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. Deploy Application

```bash
# Build and start services
docker-compose -f docker-compose.prod.yml up -d --build

# Run database migrations
docker-compose -f docker-compose.prod.yml exec web alembic upgrade head

# Load initial data
docker-compose -f docker-compose.prod.yml exec web python scripts/load_initial_data.py
```

## 🔧 Configuration Management

### Environment Variables

Production environment variables should be stored securely:

```bash
# Create secure environment file
sudo mkdir -p /etc/poe2-optimizer
sudo touch /etc/poe2-optimizer/.env
sudo chmod 600 /etc/poe2-optimizer/.env
sudo chown root:root /etc/poe2-optimizer/.env
```

### Secrets Management

For sensitive data like API keys and database passwords:

```bash
# Use Docker secrets
echo "your_secret_key" | docker secret create poe2_secret_key -
echo "your_db_password" | docker secret create poe2_db_password -
```

Update `docker-compose.prod.yml`:
```yaml
services:
  web:
    secrets:
      - poe2_secret_key
      - poe2_db_password

secrets:
  poe2_secret_key:
    external: true
  poe2_db_password:
    external: true
```

## 📊 Monitoring and Logging

### Application Monitoring

Create `docker-compose.monitoring.yml`:
```yaml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana

volumes:
  grafana-storage:
```

### Log Management

```bash
# Configure log rotation
sudo tee /etc/logrotate.d/poe2-optimizer << EOF
/var/log/poe2-optimizer/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        docker-compose -f /path/to/docker-compose.prod.yml restart web
    endscript
}
EOF
```

### Health Checks

Add health check endpoint to your application and configure monitoring:

```bash
# Create health check script
cat > /usr/local/bin/poe2-health-check.sh << 'EOF'
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health)
if [ $response -eq 200 ]; then
    echo "Application is healthy"
    exit 0
else
    echo "Application is unhealthy (HTTP $response)"
    exit 1
fi
EOF

chmod +x /usr/local/bin/poe2-health-check.sh

# Add to crontab for monitoring
echo "*/5 * * * * /usr/local/bin/poe2-health-check.sh" | crontab -
```

## 🔄 Backup and Recovery

### Database Backup

```bash
# Create backup script
cat > /usr/local/bin/poe2-backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/poe2-optimizer"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h localhost -U poe2_user poe2_optimizer | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Application backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz /path/to/poe2-tablet-optimizer

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
EOF

chmod +x /usr/local/bin/poe2-backup.sh

# Schedule daily backups
echo "0 2 * * * /usr/local/bin/poe2-backup.sh" | crontab -
```

### Recovery Procedure

```bash
# Stop application
docker-compose -f docker-compose.prod.yml down

# Restore database
gunzip -c /var/backups/poe2-optimizer/db_backup_YYYYMMDD_HHMMSS.sql.gz | psql -h localhost -U poe2_user poe2_optimizer

# Restore application files
tar -xzf /var/backups/poe2-optimizer/app_backup_YYYYMMDD_HHMMSS.tar.gz -C /

# Start application
docker-compose -f docker-compose.prod.yml up -d
```

## 🚀 Scaling and Performance

### Horizontal Scaling

```yaml
# docker-compose.scale.yml
version: '3.8'

services:
  web:
    build: .
    env_file:
      - .env.production
    depends_on:
      - redis
      - db
    deploy:
      replicas: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - web
```

### Load Balancer Configuration

```nginx
# nginx-lb.conf
upstream app_servers {
    server web_1:8000;
    server web_2:8000;
    server web_3:8000;
}

server {
    listen 80;
    location / {
        proxy_pass http://app_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Database Optimization

```sql
-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_market_data_modifier_id ON market_data(modifier_id);
CREATE INDEX CONCURRENTLY idx_market_data_last_updated ON market_data(last_updated);
CREATE INDEX CONCURRENTLY idx_price_history_modifier_timestamp ON price_history(modifier_id, timestamp);

-- Analyze tables
ANALYZE market_data;
ANALYZE price_history;
ANALYZE modifiers;
```

## 🔒 Security Hardening

### Firewall Configuration

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### Application Security

```bash
# Set secure file permissions
sudo chown -R www-data:www-data /path/to/application
sudo chmod -R 755 /path/to/application
sudo chmod 600 /path/to/application/.env.production

# Disable unnecessary services
sudo systemctl disable apache2
sudo systemctl stop apache2
```

### Regular Security Updates

```bash
# Create update script
cat > /usr/local/bin/security-updates.sh << 'EOF'
#!/bin/bash
apt update
apt upgrade -y
apt autoremove -y
docker system prune -f
EOF

chmod +x /usr/local/bin/security-updates.sh

# Schedule weekly security updates
echo "0 3 * * 0 /usr/local/bin/security-updates.sh" | crontab -
```

## 📈 Performance Tuning

### Application Optimization

```python
# gunicorn.conf.py
bind = "0.0.0.0:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
```

### Database Tuning

```postgresql
-- postgresql.conf optimizations
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

## 🔍 Troubleshooting

### Common Issues

**Application won't start:**
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs web

# Check database connection
docker-compose -f docker-compose.prod.yml exec web python -c "from app.database import engine; print(engine.execute('SELECT 1').scalar())"
```

**High memory usage:**
```bash
# Monitor resource usage
docker stats

# Adjust worker count
# Edit gunicorn.conf.py and reduce workers
```

**Database performance issues:**
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE tablename = 'market_data';
```

This deployment guide provides a comprehensive approach to deploying the PoE 2 Tablet Optimizer in production with proper security, monitoring, and scaling considerations.
