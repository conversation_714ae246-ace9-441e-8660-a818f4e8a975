"""
Real-time updates and alerts service for PoE 2 Tablet Optimizer.
Handles WebSocket connections, price alerts, and live data streaming.
"""
import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func

from app.models.market import MarketData, PriceHistory
from app.models.modifier import Modifier
from app.schemas.alerts import PriceAlert, MarketAlert, AlertSubscription
from app.database import SessionLocal
from app.config import settings

logger = logging.getLogger(__name__)


class AlertManager:
    """Manages price alerts and notifications."""
    
    def __init__(self):
        self.active_alerts: Dict[int, PriceAlert] = {}
        self.subscribers: Set[str] = set()
        self.alert_thresholds = {
            'price_spike': 15.0,      # 15% price increase
            'price_drop': -15.0,      # 15% price decrease
            'volume_spike': 50.0,     # 50% volume increase
            'volatility_high': 0.4,   # High volatility threshold
        }
    
    async def check_price_alerts(self, db: Session) -> List[PriceAlert]:
        """Check for price alerts based on recent market data."""
        alerts = []
        
        try:
            # Get recent market data changes
            recent_cutoff = datetime.utcnow() - timedelta(hours=1)
            
            market_data = db.query(MarketData).filter(
                MarketData.last_updated >= recent_cutoff
            ).all()
            
            for md in market_data:
                # Check for price spikes
                if md.price_change_24h >= self.alert_thresholds['price_spike']:
                    alert = await self._create_price_alert(
                        md, 'price_spike', 'Price spike detected'
                    )
                    alerts.append(alert)
                
                # Check for price drops
                elif md.price_change_24h <= self.alert_thresholds['price_drop']:
                    alert = await self._create_price_alert(
                        md, 'price_drop', 'Significant price drop detected'
                    )
                    alerts.append(alert)
                
                # Check for volume spikes
                if hasattr(md, 'volume_change_24h') and md.volume_change_24h >= self.alert_thresholds['volume_spike']:
                    alert = await self._create_price_alert(
                        md, 'volume_spike', 'Trading volume spike detected'
                    )
                    alerts.append(alert)
                
                # Check for high volatility
                if md.volatility_score >= self.alert_thresholds['volatility_high']:
                    alert = await self._create_price_alert(
                        md, 'high_volatility', 'High volatility detected'
                    )
                    alerts.append(alert)
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error checking price alerts: {e}")
            return []
    
    async def _create_price_alert(self, market_data: MarketData, alert_type: str, message: str) -> PriceAlert:
        """Create a price alert from market data."""
        return PriceAlert(
            modifier_id=market_data.modifier_id,
            modifier_name=market_data.modifier.display_name if market_data.modifier else "Unknown",
            alert_type=alert_type,
            message=message,
            current_price=market_data.current_price,
            price_change_percent=market_data.price_change_24h,
            volume_change_percent=getattr(market_data, 'volume_change_24h', 0.0),
            timestamp=datetime.utcnow(),
            severity=self._calculate_alert_severity(market_data, alert_type)
        )
    
    def _calculate_alert_severity(self, market_data: MarketData, alert_type: str) -> str:
        """Calculate alert severity based on market data and alert type."""
        if alert_type in ['price_spike', 'price_drop']:
            change = abs(market_data.price_change_24h)
            if change >= 30:
                return 'critical'
            elif change >= 20:
                return 'high'
            else:
                return 'medium'
        
        elif alert_type == 'volume_spike':
            return 'medium'
        
        elif alert_type == 'high_volatility':
            if market_data.volatility_score >= 0.6:
                return 'high'
            else:
                return 'medium'
        
        return 'low'
    
    async def create_market_overview_alert(self, db: Session) -> Optional[MarketAlert]:
        """Create market-wide alert based on overall conditions."""
        try:
            # Get market statistics
            total_modifiers = db.query(MarketData).count()
            if total_modifiers == 0:
                return None
            
            # Calculate market-wide metrics
            trending_up = db.query(MarketData).filter(MarketData.price_change_24h > 5).count()
            trending_down = db.query(MarketData).filter(MarketData.price_change_24h < -5).count()
            high_volatility = db.query(MarketData).filter(MarketData.volatility_score > 0.4).count()
            
            # Calculate percentages
            up_percentage = (trending_up / total_modifiers) * 100
            down_percentage = (trending_down / total_modifiers) * 100
            volatile_percentage = (high_volatility / total_modifiers) * 100
            
            # Determine market condition
            if up_percentage > 60:
                condition = "bullish"
                message = f"Strong bullish market: {up_percentage:.1f}% of modifiers trending up"
                severity = "high"
            elif down_percentage > 60:
                condition = "bearish"
                message = f"Strong bearish market: {down_percentage:.1f}% of modifiers trending down"
                severity = "high"
            elif volatile_percentage > 40:
                condition = "volatile"
                message = f"High market volatility: {volatile_percentage:.1f}% of modifiers highly volatile"
                severity = "medium"
            else:
                condition = "stable"
                message = "Market conditions are stable"
                severity = "low"
            
            return MarketAlert(
                alert_type="market_condition",
                condition=condition,
                message=message,
                severity=severity,
                timestamp=datetime.utcnow(),
                metrics={
                    "trending_up_percentage": up_percentage,
                    "trending_down_percentage": down_percentage,
                    "volatile_percentage": volatile_percentage,
                    "total_modifiers": total_modifiers
                }
            )
            
        except Exception as e:
            logger.error(f"Error creating market overview alert: {e}")
            return None


class RealTimeDataStreamer:
    """Handles real-time data streaming and WebSocket connections."""
    
    def __init__(self):
        self.connections: Dict[str, Any] = {}
        self.alert_manager = AlertManager()
        self.update_interval = 30  # seconds
        self.is_running = False
    
    async def start_streaming(self):
        """Start the real-time data streaming service."""
        self.is_running = True
        logger.info("Starting real-time data streaming service")
        
        while self.is_running:
            try:
                await self._update_and_broadcast()
                await asyncio.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error in streaming loop: {e}")
                await asyncio.sleep(5)  # Short delay before retry
    
    def stop_streaming(self):
        """Stop the real-time data streaming service."""
        self.is_running = False
        logger.info("Stopping real-time data streaming service")
    
    async def _update_and_broadcast(self):
        """Update data and broadcast to connected clients."""
        db = SessionLocal()
        try:
            # Check for alerts
            price_alerts = await self.alert_manager.check_price_alerts(db)
            market_alert = await self.alert_manager.create_market_overview_alert(db)
            
            # Prepare update data
            update_data = {
                'timestamp': datetime.utcnow().isoformat(),
                'type': 'market_update',
                'price_alerts': [alert.dict() for alert in price_alerts],
                'market_alert': market_alert.dict() if market_alert else None,
                'market_stats': await self._get_market_stats(db)
            }
            
            # Broadcast to all connected clients
            await self._broadcast_update(update_data)
            
            # Log significant alerts
            if price_alerts:
                logger.info(f"Broadcasting {len(price_alerts)} price alerts")
            
            if market_alert and market_alert.severity in ['high', 'critical']:
                logger.warning(f"Market alert: {market_alert.message}")
            
        except Exception as e:
            logger.error(f"Error updating and broadcasting: {e}")
        finally:
            db.close()
    
    async def _get_market_stats(self, db: Session) -> Dict[str, Any]:
        """Get current market statistics."""
        try:
            total_modifiers = db.query(MarketData).count()
            trending_up = db.query(MarketData).filter(MarketData.price_change_24h > 5).count()
            trending_down = db.query(MarketData).filter(MarketData.price_change_24h < -5).count()
            high_volatility = db.query(MarketData).filter(MarketData.volatility_score > 0.4).count()
            
            # Calculate average price change
            avg_change = db.query(func.avg(MarketData.price_change_24h)).scalar() or 0.0
            
            return {
                'total_modifiers': total_modifiers,
                'trending_up': trending_up,
                'trending_down': trending_down,
                'high_volatility': high_volatility,
                'average_change_24h': float(avg_change),
                'last_updated': datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting market stats: {e}")
            return {}
    
    async def _broadcast_update(self, data: Dict[str, Any]):
        """Broadcast update to all connected WebSocket clients."""
        if not self.connections:
            return
        
        message = json.dumps(data)
        disconnected = []
        
        for connection_id, websocket in self.connections.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.warning(f"Failed to send to connection {connection_id}: {e}")
                disconnected.append(connection_id)
        
        # Remove disconnected clients
        for connection_id in disconnected:
            self.connections.pop(connection_id, None)
    
    async def add_connection(self, connection_id: str, websocket):
        """Add a new WebSocket connection."""
        self.connections[connection_id] = websocket
        logger.info(f"Added WebSocket connection: {connection_id}")
        
        # Send initial data
        db = SessionLocal()
        try:
            initial_data = {
                'timestamp': datetime.utcnow().isoformat(),
                'type': 'connection_established',
                'market_stats': await self._get_market_stats(db)
            }
            await websocket.send_text(json.dumps(initial_data))
        except Exception as e:
            logger.error(f"Error sending initial data: {e}")
        finally:
            db.close()
    
    def remove_connection(self, connection_id: str):
        """Remove a WebSocket connection."""
        if connection_id in self.connections:
            del self.connections[connection_id]
            logger.info(f"Removed WebSocket connection: {connection_id}")


class NotificationService:
    """Handles various types of notifications and alerts."""
    
    def __init__(self):
        self.notification_queue = asyncio.Queue()
        self.subscribers = {}
    
    async def send_notification(self, notification: Dict[str, Any]):
        """Send a notification to subscribers."""
        await self.notification_queue.put(notification)
    
    async def process_notifications(self):
        """Process notifications from the queue."""
        while True:
            try:
                notification = await self.notification_queue.get()
                await self._deliver_notification(notification)
                self.notification_queue.task_done()
            except Exception as e:
                logger.error(f"Error processing notification: {e}")
    
    async def _deliver_notification(self, notification: Dict[str, Any]):
        """Deliver notification to appropriate channels."""
        notification_type = notification.get('type', 'general')
        
        # Log notification
        logger.info(f"Delivering notification: {notification.get('message', 'No message')}")
        
        # Here you could add integrations with:
        # - Email services
        # - Discord webhooks
        # - Slack notifications
        # - Push notifications
        # - etc.
        
        # For now, just log the notification
        if notification.get('severity') in ['high', 'critical']:
            logger.warning(f"High priority notification: {notification}")


# Global instances
real_time_streamer = RealTimeDataStreamer()
notification_service = NotificationService()


async def start_real_time_services():
    """Start all real-time services."""
    logger.info("Starting real-time services...")
    
    # Start the data streamer
    streamer_task = asyncio.create_task(real_time_streamer.start_streaming())
    
    # Start the notification processor
    notification_task = asyncio.create_task(notification_service.process_notifications())
    
    return streamer_task, notification_task


async def stop_real_time_services():
    """Stop all real-time services."""
    logger.info("Stopping real-time services...")
    real_time_streamer.stop_streaming()


# WebSocket endpoint handler
async def websocket_handler(websocket, connection_id: str):
    """Handle WebSocket connections for real-time updates."""
    try:
        await real_time_streamer.add_connection(connection_id, websocket)
        
        # Keep connection alive
        while True:
            try:
                # Wait for messages from client (ping/pong, subscriptions, etc.)
                message = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                
                # Handle client messages
                try:
                    data = json.loads(message)
                    await handle_client_message(connection_id, data)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON from client {connection_id}: {message}")
                
            except asyncio.TimeoutError:
                # Send ping to keep connection alive
                await websocket.send_text(json.dumps({
                    'type': 'ping',
                    'timestamp': datetime.utcnow().isoformat()
                }))
            
    except Exception as e:
        logger.error(f"WebSocket error for connection {connection_id}: {e}")
    finally:
        real_time_streamer.remove_connection(connection_id)


async def handle_client_message(connection_id: str, data: Dict[str, Any]):
    """Handle messages from WebSocket clients."""
    message_type = data.get('type')
    
    if message_type == 'subscribe_alerts':
        # Handle alert subscription
        alert_types = data.get('alert_types', [])
        logger.info(f"Client {connection_id} subscribed to alerts: {alert_types}")
    
    elif message_type == 'pong':
        # Handle pong response
        logger.debug(f"Received pong from client {connection_id}")
    
    elif message_type == 'request_update':
        # Handle manual update request
        db = SessionLocal()
        try:
            market_stats = await real_time_streamer._get_market_stats(db)
            response = {
                'type': 'manual_update',
                'timestamp': datetime.utcnow().isoformat(),
                'market_stats': market_stats
            }
            
            websocket = real_time_streamer.connections.get(connection_id)
            if websocket:
                await websocket.send_text(json.dumps(response))
        finally:
            db.close()
    
    else:
        logger.warning(f"Unknown message type from client {connection_id}: {message_type}")


# Utility functions for manual alert testing
async def trigger_test_alert():
    """Trigger a test alert for development/testing."""
    test_alert = {
        'type': 'test_alert',
        'message': 'This is a test alert',
        'severity': 'medium',
        'timestamp': datetime.utcnow().isoformat()
    }
    
    await notification_service.send_notification(test_alert)
    
    # Also broadcast via WebSocket
    await real_time_streamer._broadcast_update({
        'type': 'test_alert',
        'alert': test_alert
    })


async def get_active_connections_count() -> int:
    """Get the number of active WebSocket connections."""
    return len(real_time_streamer.connections)
