"""
Test configuration and fixtures for PoE 2 Tablet Optimizer tests.
"""
import pytest
import asyncio
from datetime import datetime
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.database import Base, get_db
from app.models.tablet import TabletType
from app.models.modifier import Modifier
from app.models.market import MarketData
from app.config import settings

# Test database URL (use in-memory SQLite for tests)
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
test_engine = create_engine(
    TEST_DATABASE_URL, 
    connect_args={"check_same_thread": False}
)

# Create test session
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    # Create tables
    Base.metadata.create_all(bind=test_engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop tables after test
        Base.metadata.drop_all(bind=test_engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def sample_tablet_type(db_session):
    """Create a sample tablet type for testing."""
    tablet_type = TabletType(
        name="atlas_passive",
        display_name="Atlas Passive",
        description="Atlas passive skill tree tablet",
        base_cost=0.5,
        crafting_cost=0.3
    )
    db_session.add(tablet_type)
    db_session.commit()
    db_session.refresh(tablet_type)
    return tablet_type


@pytest.fixture
def sample_modifier(db_session, sample_tablet_type):
    """Create a sample modifier for testing."""
    modifier = Modifier(
        name="increased_damage",
        display_name="Increased Damage",
        description="Increases damage dealt",
        category="damage",
        tablet_type_id=sample_tablet_type.id,
        base_value=1.5,
        current_value=1.8,
        is_valuable=True,
        tier_scaling=0.2
    )
    db_session.add(modifier)
    db_session.commit()
    db_session.refresh(modifier)
    return modifier


@pytest.fixture
def sample_market_data(db_session, sample_modifier):
    """Create sample market data for testing."""
    market_data = MarketData(
        modifier_id=sample_modifier.id,
        current_price=1.8,
        price_change_24h=5.5,
        volume_24h=150,
        volatility_score=0.25,
        confidence_score=0.85,
        last_updated=datetime.utcnow()
    )
    db_session.add(market_data)
    db_session.commit()
    db_session.refresh(market_data)
    return market_data


@pytest.fixture
def sample_tablet_data():
    """Sample tablet configuration for testing."""
    return {
        "tablet_type_id": 1,
        "modifier1_id": 1,
        "modifier2_id": None,
        "modifier1_tier": 3,
        "modifier2_tier": 1,
        "market_condition": "normal",
        "time_horizon": "immediate",
        "risk_tolerance": "moderate",
        "include_predictions": True
    }


@pytest.fixture
def mock_poe_api_response():
    """Mock response from PoE API."""
    return {
        "result": [
            {
                "id": "test_item_1",
                "listing": {
                    "price": {
                        "amount": 2.5,
                        "currency": "exalted"
                    }
                },
                "item": {
                    "name": "Test Tablet",
                    "typeLine": "Atlas Passive Tablet",
                    "properties": [
                        {
                            "name": "Increased Damage",
                            "values": [["15%", 0]]
                        }
                    ]
                }
            }
        ]
    }


@pytest.fixture
def test_regex_patterns():
    """Sample regex patterns for testing."""
    return [
        {
            "pattern": r"increased.*damage",
            "description": "Matches increased damage modifiers",
            "category": "damage"
        },
        {
            "pattern": r"(\d+)%.*life",
            "description": "Matches life percentage modifiers",
            "category": "defense"
        }
    ]


# Test data factories
class TestDataFactory:
    """Factory for creating test data."""
    
    @staticmethod
    def create_tablet_type(db_session, **kwargs):
        """Create a tablet type with optional overrides."""
        defaults = {
            "name": "test_tablet",
            "display_name": "Test Tablet",
            "description": "Test tablet description",
            "base_cost": 1.0,
            "crafting_cost": 0.5
        }
        defaults.update(kwargs)
        
        tablet_type = TabletType(**defaults)
        db_session.add(tablet_type)
        db_session.commit()
        db_session.refresh(tablet_type)
        return tablet_type
    
    @staticmethod
    def create_modifier(db_session, tablet_type_id, **kwargs):
        """Create a modifier with optional overrides."""
        defaults = {
            "name": "test_modifier",
            "display_name": "Test Modifier",
            "description": "Test modifier description",
            "category": "test",
            "tablet_type_id": tablet_type_id,
            "base_value": 1.0,
            "current_value": 1.2,
            "is_valuable": True,
            "tier_scaling": 0.15
        }
        defaults.update(kwargs)
        
        modifier = Modifier(**defaults)
        db_session.add(modifier)
        db_session.commit()
        db_session.refresh(modifier)
        return modifier
    
    @staticmethod
    def create_market_data(db_session, modifier_id, **kwargs):
        """Create market data with optional overrides."""
        from datetime import datetime
        
        defaults = {
            "modifier_id": modifier_id,
            "current_price": 1.5,
            "price_change_24h": 0.0,
            "volume_24h": 100,
            "volatility_score": 0.2,
            "confidence_score": 0.8,
            "last_updated": datetime.utcnow()
        }
        defaults.update(kwargs)
        
        market_data = MarketData(**defaults)
        db_session.add(market_data)
        db_session.commit()
        db_session.refresh(market_data)
        return market_data


@pytest.fixture
def test_factory():
    """Provide access to test data factory."""
    return TestDataFactory


# Mock external services
@pytest.fixture
def mock_poe_api(monkeypatch):
    """Mock PoE API calls."""
    class MockPoEAPI:
        def __init__(self):
            self.call_count = 0
        
        async def fetch_market_data(self, *args, **kwargs):
            self.call_count += 1
            return {
                "success": True,
                "data": [
                    {
                        "id": "mock_item",
                        "price": 2.0,
                        "currency": "exalted"
                    }
                ]
            }
        
        async def search_items(self, *args, **kwargs):
            self.call_count += 1
            return {
                "id": "mock_search_id",
                "total": 10
            }
    
    mock_api = MockPoEAPI()
    
    # Patch the actual API calls
    monkeypatch.setattr("app.services.market_service.MarketService.fetch_market_data", mock_api.fetch_market_data)
    monkeypatch.setattr("app.services.market_service.MarketService.search_items", mock_api.search_items)
    
    return mock_api


# Performance testing helpers
@pytest.fixture
def performance_timer():
    """Timer for performance testing."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


# Async test helpers
@pytest.fixture
def async_client(client):
    """Async test client wrapper."""
    class AsyncTestClient:
        def __init__(self, sync_client):
            self.client = sync_client
        
        async def get(self, *args, **kwargs):
            return self.client.get(*args, **kwargs)
        
        async def post(self, *args, **kwargs):
            return self.client.post(*args, **kwargs)
        
        async def put(self, *args, **kwargs):
            return self.client.put(*args, **kwargs)
        
        async def delete(self, *args, **kwargs):
            return self.client.delete(*args, **kwargs)
    
    return AsyncTestClient(client)


# Test configuration
@pytest.fixture(autouse=True)
def test_settings(monkeypatch):
    """Override settings for testing."""
    monkeypatch.setattr(settings, "TESTING", True)
    monkeypatch.setattr(settings, "DATABASE_URL", TEST_DATABASE_URL)
    monkeypatch.setattr(settings, "POE_API_RATE_LIMIT", 10)  # Higher rate limit for tests
    monkeypatch.setattr(settings, "CACHE_TTL", 1)  # Short cache TTL for tests


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Cleanup after each test."""
    yield
    # Any cleanup code here
    pass
