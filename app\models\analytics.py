"""
Analytics and machine learning models.
"""
from sqlalchemy import <PERSON>um<PERSON>, Integer, String, Text, Boolean, ForeignKey, DateTime, Float, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class PredictionModel(Base):
    """
    Machine learning models for price prediction and trend analysis.
    Stores model metadata and performance metrics.
    """
    __tablename__ = "prediction_models"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Model identification
    name = Column(String(100), nullable=False, unique=True)
    model_type = Column(String(50), nullable=False)  # "linear_regression", "random_forest", etc.
    version = Column(String(20), nullable=False)
    description = Column(Text)
    
    # Model configuration
    parameters = Column(JSON)  # Model hyperparameters
    features = Column(JSON)  # List of features used
    target_variable = Column(String(100))  # What the model predicts
    
    # Performance metrics
    accuracy_score = Column(Float, default=0.0)
    precision_score = Column(Float, default=0.0)
    recall_score = Column(Float, default=0.0)
    f1_score = Column(Float, default=0.0)
    mse = Column(Float, default=0.0)  # Mean Squared Error
    mae = Column(Float, default=0.0)  # Mean Absolute Error
    r2_score = Column(Float, default=0.0)  # R-squared
    
    # Training data
    training_data_size = Column(Integer, default=0)
    training_date_from = Column(DateTime(timezone=True))
    training_date_to = Column(DateTime(timezone=True))
    validation_split = Column(Float, default=0.2)
    
    # Model status
    is_active = Column(Boolean, default=True)
    is_production = Column(Boolean, default=False)
    last_retrained = Column(DateTime(timezone=True))
    next_retrain_due = Column(DateTime(timezone=True))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(100))  # Who created the model
    
    # Relationships
    trend_analyses = relationship("TrendAnalysis", back_populates="model")
    
    def __repr__(self):
        return f"<PredictionModel(name='{self.name}', type='{self.model_type}', version='{self.version}')>"
    
    @property
    def performance_summary(self) -> dict:
        """Get summary of model performance."""
        return {
            "accuracy": self.accuracy_score,
            "precision": self.precision_score,
            "recall": self.recall_score,
            "f1": self.f1_score,
            "mse": self.mse,
            "mae": self.mae,
            "r2": self.r2_score
        }


class TrendAnalysis(Base):
    """
    Results from trend analysis algorithms.
    Stores predictions and analysis results for specific time periods.
    """
    __tablename__ = "trend_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, ForeignKey("prediction_models.id"), nullable=False)
    modifier_id = Column(Integer, ForeignKey("modifiers.id"), nullable=False)
    
    # Analysis period
    analysis_date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    
    # Trend detection
    trend_direction = Column(String(20))  # "up", "down", "sideways"
    trend_strength = Column(Float, default=0.0)  # 0-1 scale
    trend_confidence = Column(Float, default=0.0)  # Model confidence
    change_points = Column(JSON)  # Detected trend change points
    
    # Price predictions
    predicted_prices = Column(JSON)  # Time series of predicted prices
    prediction_intervals = Column(JSON)  # Confidence intervals
    forecast_horizon_days = Column(Integer, default=30)
    
    # Statistical measures
    volatility_forecast = Column(Float, default=0.0)
    correlation_score = Column(Float, default=0.0)
    seasonality_detected = Column(Boolean, default=False)
    seasonal_pattern = Column(JSON)  # Seasonal pattern data
    
    # Market indicators
    momentum_indicator = Column(Float, default=0.0)
    mean_reversion_score = Column(Float, default=0.0)
    breakout_probability = Column(Float, default=0.0)
    support_resistance_levels = Column(JSON)
    
    # Feature importance
    feature_importance = Column(JSON)  # Which features drove the prediction
    model_explanation = Column(Text)  # Human-readable explanation
    
    # Quality metrics
    prediction_accuracy = Column(Float, default=0.0)  # How accurate past predictions were
    data_quality_score = Column(Float, default=0.0)  # Quality of input data
    outliers_detected = Column(Integer, default=0)
    
    # Metadata
    computation_time_ms = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    model = relationship("PredictionModel", back_populates="trend_analyses")
    modifier = relationship("Modifier")
    
    def __repr__(self):
        return f"<TrendAnalysis(modifier='{self.modifier.name}', trend='{self.trend_direction}', date={self.analysis_date})>"
    
    @property
    def is_reliable(self) -> bool:
        """Check if analysis is reliable based on confidence and data quality."""
        from app.config import data_config
        return (
            self.trend_confidence > data_config.CONFIDENCE_THRESHOLD and
            self.data_quality_score > data_config.CONFIDENCE_THRESHOLD
        )
    
    @property
    def recommendation(self) -> str:
        """Get trading recommendation based on analysis."""
        if not self.is_reliable:
            return "insufficient_data"
        
        if self.trend_direction == "up" and self.trend_strength > 0.7:
            return "strong_buy"
        elif self.trend_direction == "up" and self.trend_strength > 0.5:
            return "buy"
        elif self.trend_direction == "down" and self.trend_strength > 0.7:
            return "strong_sell"
        elif self.trend_direction == "down" and self.trend_strength > 0.5:
            return "sell"
        else:
            return "hold"
