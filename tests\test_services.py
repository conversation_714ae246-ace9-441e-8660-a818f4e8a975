"""
Service layer tests for PoE 2 Tablet Optimizer.
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

from app.services.valuation_service import Val<PERSON><PERSON><PERSON><PERSON>, SynergyCalculator
from app.services.market_service import MarketService
from app.services.analytics_service import PredictiveAnalyticsService
from app.services.regex_service import RegexPatternGenerator
from app.services.realtime_service import AlertManager, RealTimeDataStreamer


class TestValuationService:
    """Test valuation service functionality."""
    
    def test_synergy_calculator_initialization(self):
        """Test synergy calculator initialization."""
        calculator = SynergyCalculator()
        
        assert calculator.category_synergies is not None
        assert calculator.specific_synergies is not None
        assert len(calculator.category_synergies) > 0
    
    def test_calculate_category_synergy(self):
        """Test category synergy calculation."""
        calculator = SynergyCalculator()
        
        # Test damage + damage synergy
        synergy = calculator.calculate_category_synergy(['damage', 'damage'])
        assert synergy > 0
        
        # Test damage + defense synergy
        synergy = calculator.calculate_category_synergy(['damage', 'defense'])
        assert synergy >= 0
        
        # Test single category (no synergy)
        synergy = calculator.calculate_category_synergy(['damage'])
        assert synergy == 0
    
    def test_calculate_specific_synergy(self):
        """Test specific modifier synergy calculation."""
        calculator = SynergyCalculator()
        
        # Test known synergy combination
        synergy = calculator.calculate_specific_synergy(['increased_damage', 'critical_strike_chance'])
        assert synergy >= 0
        
        # Test unknown combination
        synergy = calculator.calculate_specific_synergy(['unknown_mod1', 'unknown_mod2'])
        assert synergy == 0
    
    def test_valuation_engine_initialization(self, db_session):
        """Test valuation engine initialization."""
        engine = ValuationEngine(db_session)
        
        assert engine.db == db_session
        assert engine.synergy_calculator is not None
    
    @pytest.mark.asyncio
    async def test_evaluate_tablet_basic(self, db_session, sample_tablet_type, sample_modifier):
        """Test basic tablet evaluation."""
        engine = ValuationEngine(db_session)
        
        tablet_config = {
            'tablet_type_id': sample_tablet_type.id,
            'modifier1_id': sample_modifier.id,
            'modifier1_tier': 3,
            'market_condition': 'normal',
            'time_horizon': 'immediate',
            'risk_tolerance': 'moderate'
        }
        
        result = await engine.evaluate_tablet(tablet_config)
        
        assert 'total_value' in result
        assert 'profit_estimate' in result
        assert 'recommendation' in result
        assert 'confidence_score' in result
        assert 'breakdown' in result
        
        assert isinstance(result['total_value'], (int, float))
        assert isinstance(result['profit_estimate'], (int, float))
        assert 0 <= result['confidence_score'] <= 1
    
    @pytest.mark.asyncio
    async def test_evaluate_tablet_with_synergy(self, db_session, test_factory, sample_tablet_type):
        """Test tablet evaluation with synergy bonuses."""
        # Create two modifiers that should have synergy
        mod1 = test_factory.create_modifier(
            db_session, 
            sample_tablet_type.id,
            name="increased_damage",
            category="damage",
            current_value=2.0
        )
        mod2 = test_factory.create_modifier(
            db_session,
            sample_tablet_type.id,
            name="critical_strike_chance",
            category="damage",
            current_value=1.5
        )
        
        engine = ValuationEngine(db_session)
        
        tablet_config = {
            'tablet_type_id': sample_tablet_type.id,
            'modifier1_id': mod1.id,
            'modifier2_id': mod2.id,
            'modifier1_tier': 3,
            'modifier2_tier': 3,
            'market_condition': 'normal',
            'time_horizon': 'immediate',
            'risk_tolerance': 'moderate'
        }
        
        result = await engine.evaluate_tablet(tablet_config)
        
        # Should have synergy bonus
        assert result.get('synergy_bonus', 0) > 0


class TestMarketService:
    """Test market service functionality."""
    
    def test_market_service_initialization(self):
        """Test market service initialization."""
        service = MarketService()
        
        assert service.session_id is not None
        assert service.base_url is not None
        assert service.rate_limiter is not None
    
    @pytest.mark.asyncio
    async def test_fetch_market_data_mock(self, mock_poe_api):
        """Test fetching market data with mocked API."""
        service = MarketService()
        
        result = await service.fetch_market_data("test_query")
        
        assert result is not None
        assert mock_poe_api.call_count > 0
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self):
        """Test rate limiting functionality."""
        service = MarketService()
        
        # Make multiple rapid requests
        start_time = datetime.now()
        
        for _ in range(3):
            await service.rate_limiter.acquire()
        
        end_time = datetime.now()
        elapsed = (end_time - start_time).total_seconds()
        
        # Should take some time due to rate limiting
        assert elapsed > 0
    
    def test_parse_item_data(self):
        """Test parsing item data from PoE API response."""
        service = MarketService()
        
        sample_item = {
            "id": "test_item",
            "listing": {
                "price": {
                    "amount": 2.5,
                    "currency": "exalted"
                }
            },
            "item": {
                "name": "Test Tablet",
                "typeLine": "Atlas Passive Tablet",
                "properties": [
                    {
                        "name": "Increased Damage",
                        "values": [["15%", 0]]
                    }
                ]
            }
        }
        
        parsed = service._parse_item_data(sample_item)
        
        assert parsed['price'] == 2.5
        assert parsed['currency'] == 'exalted'
        assert parsed['name'] == 'Test Tablet'
        assert 'modifiers' in parsed


class TestAnalyticsService:
    """Test analytics service functionality."""
    
    def test_analytics_service_initialization(self, db_session):
        """Test analytics service initialization."""
        service = PredictiveAnalyticsService(db_session)
        
        assert service.db == db_session
        assert service.models is not None
    
    @pytest.mark.asyncio
    async def test_generate_predictions(self, db_session, sample_modifier, sample_market_data):
        """Test generating price predictions."""
        service = PredictiveAnalyticsService(db_session)
        
        # Create some historical data
        for i in range(10):
            test_factory.create_market_data(
                db_session,
                sample_modifier.id,
                current_price=1.0 + i * 0.1,
                last_updated=datetime.utcnow() - timedelta(days=i)
            )
        
        predictions = await service.generate_predictions(sample_modifier.id)
        
        assert 'modifier_id' in predictions
        assert 'predictions' in predictions
        assert 'confidence' in predictions
        assert len(predictions['predictions']) > 0
    
    def test_calculate_technical_indicators(self, db_session, sample_modifier):
        """Test technical indicator calculations."""
        service = PredictiveAnalyticsService(db_session)
        
        # Create price history
        prices = [1.0, 1.1, 1.2, 1.1, 1.3, 1.2, 1.4, 1.3, 1.5, 1.4]
        
        indicators = service._calculate_technical_indicators(prices)
        
        assert 'rsi' in indicators
        assert 'moving_average' in indicators
        assert 'volatility' in indicators
        
        assert 0 <= indicators['rsi'] <= 100
        assert indicators['moving_average'] > 0
        assert indicators['volatility'] >= 0
    
    @pytest.mark.asyncio
    async def test_market_sentiment_analysis(self, db_session, sample_market_data):
        """Test market sentiment analysis."""
        service = PredictiveAnalyticsService(db_session)
        
        sentiment = await service.analyze_market_sentiment()
        
        assert 'overall_sentiment' in sentiment
        assert 'confidence' in sentiment
        assert 'factors' in sentiment
        
        assert sentiment['overall_sentiment'] in ['bullish', 'bearish', 'neutral']
        assert 0 <= sentiment['confidence'] <= 1


class TestRegexService:
    """Test regex generation service functionality."""
    
    def test_regex_generator_initialization(self, db_session):
        """Test regex generator initialization."""
        generator = RegexPatternGenerator(db_session)
        
        assert generator.db == db_session
    
    @pytest.mark.asyncio
    async def test_generate_individual_patterns(self, db_session, sample_modifier):
        """Test generating individual modifier patterns."""
        generator = RegexPatternGenerator(db_session)
        
        patterns = await generator.generate_individual_patterns([sample_modifier.id])
        
        assert len(patterns) > 0
        
        pattern = patterns[0]
        assert 'pattern' in pattern
        assert 'description' in pattern
        assert 'modifier_id' in pattern
    
    @pytest.mark.asyncio
    async def test_generate_combination_patterns(self, db_session, test_factory, sample_tablet_type):
        """Test generating combination patterns."""
        # Create multiple modifiers
        mod1 = test_factory.create_modifier(db_session, sample_tablet_type.id, name="mod1")
        mod2 = test_factory.create_modifier(db_session, sample_tablet_type.id, name="mod2")
        
        generator = RegexPatternGenerator(db_session)
        
        patterns = await generator.generate_combination_patterns([mod1.id, mod2.id])
        
        assert len(patterns) > 0
        
        pattern = patterns[0]
        assert 'pattern' in pattern
        assert 'description' in pattern
        assert 'modifier_ids' in pattern
    
    def test_validate_regex_pattern(self):
        """Test regex pattern validation."""
        generator = RegexPatternGenerator(None)  # No DB needed for validation
        
        # Valid pattern
        result = generator.validate_pattern(r"increased.*damage", ["increased damage", "increased fire damage"])
        
        assert result['is_valid'] is True
        assert len(result['matches']) == 2
        
        # Invalid pattern
        result = generator.validate_pattern(r"[invalid", ["test"])
        
        assert result['is_valid'] is False
        assert 'error' in result


class TestRealTimeService:
    """Test real-time service functionality."""
    
    def test_alert_manager_initialization(self):
        """Test alert manager initialization."""
        manager = AlertManager()
        
        assert manager.active_alerts is not None
        assert manager.subscribers is not None
        assert manager.alert_thresholds is not None
    
    @pytest.mark.asyncio
    async def test_check_price_alerts(self, db_session, sample_market_data):
        """Test checking for price alerts."""
        manager = AlertManager()
        
        # Create market data with significant price change
        sample_market_data.price_change_24h = 20.0  # 20% increase
        db_session.commit()
        
        alerts = await manager.check_price_alerts(db_session)
        
        assert len(alerts) > 0
        
        alert = alerts[0]
        assert alert.alert_type == 'price_spike'
        assert alert.severity in ['low', 'medium', 'high', 'critical']
    
    @pytest.mark.asyncio
    async def test_market_overview_alert(self, db_session, test_factory, sample_tablet_type):
        """Test creating market overview alerts."""
        manager = AlertManager()
        
        # Create multiple market data entries with trends
        for i in range(10):
            modifier = test_factory.create_modifier(db_session, sample_tablet_type.id, name=f"mod_{i}")
            test_factory.create_market_data(
                db_session,
                modifier.id,
                price_change_24h=10.0 if i < 7 else -5.0  # 70% trending up
            )
        
        alert = await manager.create_market_overview_alert(db_session)
        
        assert alert is not None
        assert alert.condition == 'bullish'
        assert alert.severity in ['low', 'medium', 'high']
    
    def test_real_time_streamer_initialization(self):
        """Test real-time data streamer initialization."""
        streamer = RealTimeDataStreamer()
        
        assert streamer.connections is not None
        assert streamer.alert_manager is not None
        assert streamer.update_interval > 0
        assert streamer.is_running is False
    
    @pytest.mark.asyncio
    async def test_websocket_connection_management(self):
        """Test WebSocket connection management."""
        streamer = RealTimeDataStreamer()
        
        # Mock WebSocket
        mock_websocket = Mock()
        mock_websocket.send_text = AsyncMock()
        
        # Add connection
        await streamer.add_connection("test_conn", mock_websocket)
        
        assert "test_conn" in streamer.connections
        assert mock_websocket.send_text.called
        
        # Remove connection
        streamer.remove_connection("test_conn")
        
        assert "test_conn" not in streamer.connections


class TestIntegration:
    """Integration tests for service interactions."""
    
    @pytest.mark.asyncio
    async def test_valuation_with_market_data(self, db_session, sample_tablet_type, sample_modifier, sample_market_data):
        """Test valuation service using real market data."""
        valuation_engine = ValuationEngine(db_session)
        
        tablet_config = {
            'tablet_type_id': sample_tablet_type.id,
            'modifier1_id': sample_modifier.id,
            'modifier1_tier': 3,
            'market_condition': 'normal',
            'time_horizon': 'immediate',
            'risk_tolerance': 'moderate'
        }
        
        result = await valuation_engine.evaluate_tablet(tablet_config)
        
        # Should use market data for valuation
        assert result['total_value'] > 0
        assert result['confidence_score'] > 0
    
    @pytest.mark.asyncio
    async def test_analytics_with_predictions(self, db_session, sample_modifier, sample_market_data):
        """Test analytics service generating predictions."""
        analytics_service = PredictiveAnalyticsService(db_session)
        
        # Create historical data
        for i in range(20):
            test_factory.create_market_data(
                db_session,
                sample_modifier.id,
                current_price=1.0 + (i * 0.05),
                last_updated=datetime.utcnow() - timedelta(hours=i)
            )
        
        predictions = await analytics_service.generate_predictions(sample_modifier.id)
        
        assert predictions['modifier_id'] == sample_modifier.id
        assert len(predictions['predictions']) > 0
        assert predictions['confidence'] > 0
    
    @pytest.mark.asyncio
    async def test_regex_with_market_context(self, db_session, sample_modifier, sample_market_data):
        """Test regex generation considering market context."""
        regex_generator = RegexPatternGenerator(db_session)
        
        patterns = await regex_generator.generate_individual_patterns([sample_modifier.id])
        
        assert len(patterns) > 0
        
        # Should include market-aware optimizations
        pattern = patterns[0]
        assert 'priority' in pattern or 'market_relevance' in pattern


class TestErrorHandling:
    """Test error handling in services."""
    
    @pytest.mark.asyncio
    async def test_valuation_with_invalid_modifier(self, db_session, sample_tablet_type):
        """Test valuation with invalid modifier ID."""
        engine = ValuationEngine(db_session)
        
        tablet_config = {
            'tablet_type_id': sample_tablet_type.id,
            'modifier1_id': 999,  # Non-existent
            'modifier1_tier': 3
        }
        
        with pytest.raises(Exception):
            await engine.evaluate_tablet(tablet_config)
    
    @pytest.mark.asyncio
    async def test_market_service_api_failure(self):
        """Test market service handling API failures."""
        service = MarketService()
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.side_effect = Exception("API Error")
            
            result = await service.fetch_market_data("test")
            
            # Should handle error gracefully
            assert result is None or 'error' in result
    
    @pytest.mark.asyncio
    async def test_analytics_insufficient_data(self, db_session, sample_modifier):
        """Test analytics with insufficient historical data."""
        service = PredictiveAnalyticsService(db_session)
        
        # No historical data
        predictions = await service.generate_predictions(sample_modifier.id)
        
        # Should handle gracefully
        assert 'error' in predictions or predictions['confidence'] == 0
