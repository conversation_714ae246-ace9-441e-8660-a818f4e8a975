"""
Modifier and modifier value models.
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, ForeignKey, DateTime, Float, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from app.database import Base


class ModifierCategory(PyEnum):
    """Categories for organizing modifiers."""
    DAMAGE = "damage"
    DEFENSE = "defense"
    UTILITY = "utility"
    RESOURCE = "resource"
    SPECIAL = "special"


class Modifier(Base):
    """
    Represents a modifier that can appear on tablets.
    Each modifier belongs to a specific tablet type and has market values.
    """
    __tablename__ = "modifiers"
    
    id = Column(Integer, primary_key=True, index=True)
    tablet_type_id = Column(Integer, ForeignKey("tablet_types.id"), nullable=False)
    
    # Modifier identification
    name = Column(String(200), nullable=False, index=True)
    display_name = Column(String(250), nullable=False)
    description = Column(Text)
    category = Column(Enum(ModifierCategory), default=ModifierCategory.UTILITY)
    
    # Game mechanics
    min_tier = Column(Integer, default=1)
    max_tier = Column(Integer, default=1)
    weight = Column(Integer, default=100)  # Spawn weight in game
    
    # Market data
    base_value = Column(Float, default=0.0)  # Base value in exalted orbs
    is_valuable = Column(Boolean, default=False)  # Quick filter for valuable mods
    
    # Regex patterns for inventory search
    regex_pattern = Column(String(500))  # Pattern to match this modifier
    regex_priority = Column(Integer, default=0)  # Priority for pattern generation
    
    # Metadata
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tablet_type = relationship("TabletType", back_populates="modifiers")
    modifier_values = relationship("ModifierValue", back_populates="modifier")
    market_data = relationship("MarketData", back_populates="modifier")
    
    def __repr__(self):
        return f"<Modifier(name='{self.name}', tablet_type='{self.tablet_type.name if self.tablet_type else 'Unknown'}')>"
    
    @property
    def current_value(self) -> float:
        """Get the most recent market value."""
        if self.modifier_values:
            latest = max(self.modifier_values, key=lambda x: x.updated_at)
            return latest.average_value
        return self.base_value
    
    @property
    def value_tier(self) -> str:
        """Get value tier classification."""
        from app.config import data_config
        value = self.current_value
        if value >= data_config.HIGH_VALUE_THRESHOLD:
            return "high"
        elif value >= data_config.MEDIUM_VALUE_THRESHOLD:
            return "medium"
        else:
            return "low"


class ModifierTier(Base):
    """
    Represents different tiers/rolls of a modifier.
    Higher tiers typically have better values and higher market prices.
    """
    __tablename__ = "modifier_tiers"
    
    id = Column(Integer, primary_key=True, index=True)
    modifier_id = Column(Integer, ForeignKey("modifiers.id"), nullable=False)
    
    tier = Column(Integer, nullable=False)  # Tier number (1 = lowest, higher = better)
    min_value = Column(Float)  # Minimum roll value
    max_value = Column(Float)  # Maximum roll value
    display_text = Column(String(300))  # How it appears in game
    
    # Market multiplier for this tier
    value_multiplier = Column(Float, default=1.0)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    modifier = relationship("Modifier")
    
    def __repr__(self):
        return f"<ModifierTier(modifier='{self.modifier.name}', tier={self.tier})>"


class ModifierValue(Base):
    """
    Tracks market values for modifiers over time.
    Used for trend analysis and price predictions.
    """
    __tablename__ = "modifier_values"
    
    id = Column(Integer, primary_key=True, index=True)
    modifier_id = Column(Integer, ForeignKey("modifiers.id"), nullable=False)
    
    # Value data
    average_value = Column(Float, nullable=False)  # Average market value
    min_value = Column(Float)  # Minimum observed value
    max_value = Column(Float)  # Maximum observed value
    sample_size = Column(Integer, default=1)  # Number of data points
    
    # Market metrics
    volatility = Column(Float, default=0.0)  # Price volatility (0-1)
    confidence = Column(Float, default=0.0)  # Confidence in data (0-1)
    trend_direction = Column(String(20))  # "up", "down", "stable"
    
    # Data source
    data_source = Column(String(100))  # Where the data came from
    collection_method = Column(String(100))  # How it was collected
    
    # Metadata
    valid_from = Column(DateTime(timezone=True), server_default=func.now())
    valid_to = Column(DateTime(timezone=True))  # When this value expires
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    modifier = relationship("Modifier", back_populates="modifier_values")
    
    def __repr__(self):
        return f"<ModifierValue(modifier='{self.modifier.name}', value={self.average_value})>"
