# PoE 2 Tablet Crafting Profit Optimizer - Project Goals

## 🎯 Primary Objective

**Maximize profit from Path of Exile 2 precursor tablet crafting by providing instant sell/recycle decisions.**

## 📋 Core Problem Being Solved

### The Crafting Workflow:
1. **Purchase Normal Tablets** - Buy base tablets with zero modifiers at low cost
2. **Craft with Orbs** - Use Augmentation/Alteration orbs to add 2 random modifiers (making tablets "Magic")
3. **Manual Evaluation** - Look at crafted tablets in inventory and determine value
4. **Decision Point** - Determine whether each crafted tablet should be:
   - **SOLD** for profit on the trade market
   - **RECYCLED** (vendored/rerolled) due to low value

### The Challenge:
- **Manual modifier lookup** - Need to reference which modifiers are valuable
- **Complex modifier interactions** and synergies affect pricing
- **Memory burden** - Difficult to remember all valuable modifier combinations
- **Inconsistent decisions** without clear valuation reference

## 🔧 Software Solution

### Core Features:
1. **Modifier Value Reference**
   - Complete list of all tablet types and their possible modifiers
   - Individual modifier values in exalted orbs
   - Clear value tiers (High/Medium/Low) for quick scanning

2. **Valuable Combination Highlighting**
   - Synergy bonuses for powerful modifier pairs
   - Visual highlighting of high-value combinations
   - Quick reference for profitable modifier pairs

3. **Regex Pattern Generator**
   - Generate regex strings for valuable modifiers
   - Copy-paste into PoE 2 inventory search
   - Instantly highlight sellable tablets in inventory
   - Separate patterns for different value tiers

4. **Manual Evaluation Tool**
   - Optional calculator for specific tablet evaluation
   - Input: Tablet type + 2 modifiers + tiers
   - Output: Total value and profit estimation

5. **Market-Based Valuation**
   - Values derived from real PoE 2 trade market data
   - Exalted orb pricing system
   - Regular updates to maintain accuracy

6. **Predictive Market Analysis**
   - Historical price tracking for all modifiers
   - Trend analysis showing rising/falling values
   - Predictive algorithms for future value estimation
   - Market volatility indicators

7. **Visual Market Intelligence**
   - Interactive charts showing modifier value trends over time
   - Tablet type performance comparisons
   - Market heat maps highlighting hot/cold modifiers
   - Price movement alerts for significant changes

## 💰 Economic Model

### Valuation System:
- **Base Currency**: Exalted orbs (high-value PoE 2 currency)
- **Cost Tracking**: Base tablet cost + crafting orb costs
- **Profit Calculation**: Market value - total crafting investment
- **Confidence Scoring**: Based on market data quality and volume

### Value Categories:
- **High Value**: Modifiers worth 0.8+ exalted orbs (clear sell candidates)
- **Medium Value**: Modifiers worth 0.4-0.8 exalted orbs (situational)
- **Low Value**: Modifiers worth <0.4 exalted orbs (likely recycle)
- **Synergy Bonuses**: Additional value for powerful modifier combinations

### Market Intelligence:
- **Trending Up**: Modifiers showing consistent price increases
- **Trending Down**: Modifiers losing value over time
- **Volatile**: Modifiers with high price fluctuation
- **Stable**: Modifiers with consistent pricing
- **Seasonal Patterns**: Modifiers affected by league mechanics or meta shifts

## 🎮 User Experience Goals

### Simplicity:
- **Quick reference tool** - No complex input required
- **Visual clarity** - Easy to scan modifier values at a glance
- **Organized by tablet type** - Find relevant information quickly

### Efficiency:
- **Instant lookup** - No waiting for calculations
- **Highlighted combinations** - Spot valuable pairs immediately
- **Comprehensive coverage** - All tablet types and modifiers included

### Accuracy:
- **Market-based values** - Derived from actual PoE 2 trade data
- **Tablet-specific modifier pools** - Each tablet type has unique modifiers
- **Predictive analytics** - Forecast future value trends
- **Real-time updates** - Values and trends stay current with market changes

### Intelligence:
- **Visual trend analysis** - Charts showing modifier performance over time
- **Market timing** - Identify optimal sell/hold periods
- **Comparative analysis** - See which tablet types are performing best

## 📊 Success Metrics

### Primary KPIs:
1. **Decision Speed**: Reduce modifier lookup time from minutes to seconds
2. **Market Intelligence**: Provide predictive insights for optimal timing
3. **Accuracy**: Reliable market-based valuations with trend analysis
4. **Coverage**: Include all tablet types and their modifier pools
5. **Usability**: Clear, scannable reference with visual trend indicators

### Technical Goals:
- **Real-time data processing** for market trend analysis
- **Interactive charts** showing modifier value trends over time
- **Predictive algorithms** for future value estimation
- **Visual highlighting** of trending modifiers and combinations
- **Historical data storage** for comprehensive trend analysis
- **Alert system** for significant market changes

## 🔄 Workflow Integration

### Before This Tool:
1. Craft tablet with random modifiers
2. Look up each modifier individually online
3. Try to remember which combinations are valuable
4. Guess at total value and profitability
5. No insight into market trends or timing
6. Make uncertain sell/recycle decisions
7. **Result**: Slow, inconsistent, often miss profitable opportunities and optimal timing

### With This Tool:
1. Craft tablet with random modifiers
2. Check market trend charts for optimal timing
3. Copy regex pattern from tool (updated with trending modifiers)
4. Paste into PoE 2 inventory search
5. Valuable tablets automatically highlighted (including trending ones)
6. Sell highlighted tablets at optimal market timing
7. **Result**: Instant identification + market intelligence for maximum profit

## 🎯 Target Outcome

**Transform tablet crafting from a time-consuming manual evaluation process into an intelligent, data-driven profit optimization system where users can:**

- **Instantly identify valuable tablets** using regex patterns in PoE 2 inventory
- **Leverage market intelligence** to time sales for maximum profit
- **Predict future value trends** to make strategic hold/sell decisions
- **Visualize market patterns** through interactive charts and trend analysis
- **Eliminate manual evaluation time** through automated highlighting
- **Maximize profits** by combining instant identification with market timing
- **Scale operations** with zero time spent on individual tablet assessment
- **Stay ahead of market shifts** with predictive analytics and alerts

**Ultimate Goal**: Enable PoE 2 players to instantly identify sellable tablets AND optimize market timing using predictive analytics, transforming tablet crafting from reactive evaluation into proactive profit maximization through data-driven market intelligence.
