"""
Data collection script for gathering market data from PoE APIs.
This script can be run periodically to update market information.
"""
import sys
import os
import asyncio
import logging
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.services.market_service import MarketService, PoEAPIClient
from app.models.modifier import Modifier
from app.models.market import MarketData, PriceHistory

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_collection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DataCollector:
    """Main data collection orchestrator."""
    
    def __init__(self):
        self.db = SessionLocal()
        self.market_service = MarketService(self.db)
        self.poe_client = PoEAPIClient()
    
    async def collect_all_data(self):
        """Collect all available data from PoE APIs."""
        try:
            logger.info("Starting comprehensive data collection...")
            
            # 1. Collect basic API data
            await self.collect_api_metadata()
            
            # 2. Update market data for all modifiers
            await self.update_market_data()
            
            # 3. Collect price history
            await self.collect_price_history()
            
            logger.info("Data collection completed successfully!")
            
        except Exception as e:
            logger.error(f"Error during data collection: {e}")
            raise
        finally:
            self.db.close()
    
    async def collect_api_metadata(self):
        """Collect metadata from PoE APIs."""
        logger.info("Collecting API metadata...")
        
        try:
            # Get leagues
            leagues = await self.poe_client.get_leagues()
            logger.info(f"Found {len(leagues)} leagues")
            for league in leagues[:5]:  # Log first 5
                logger.info(f"  - {league.get('id', 'Unknown')}: {league.get('description', 'No description')}")
            
            # Get filters
            filters = await self.poe_client.get_filters()
            if filters:
                logger.info("Retrieved trade filters successfully")
            
            # Get stats
            stats = await self.poe_client.get_stats()
            if stats:
                logger.info("Retrieved stats/modifiers successfully")
                # Log some stat categories
                if 'result' in stats:
                    for category in stats['result'][:3]:  # First 3 categories
                        logger.info(f"  - Stat category: {category.get('label', 'Unknown')}")
            
            # Get static data
            static_data = await self.poe_client.get_static_data()
            if static_data:
                logger.info("Retrieved static data successfully")
            
            # Get items
            items = await self.poe_client.get_items()
            if items:
                logger.info("Retrieved items data successfully")
                
        except Exception as e:
            logger.error(f"Error collecting API metadata: {e}")
    
    async def update_market_data(self):
        """Update market data for all active modifiers."""
        logger.info("Updating market data...")
        
        try:
            # Get all active modifiers
            modifiers = self.db.query(Modifier).filter(Modifier.is_active == True).all()
            logger.info(f"Updating market data for {len(modifiers)} modifiers")
            
            # Update market data
            result = await self.market_service.refresh_market_data()
            logger.info(f"Updated market data for {result.get('updated_count', 0)} modifiers")
            
        except Exception as e:
            logger.error(f"Error updating market data: {e}")
    
    async def collect_price_history(self):
        """Collect and store price history data."""
        logger.info("Collecting price history...")
        
        try:
            # Get all market data entries
            market_data_entries = self.db.query(MarketData).all()
            
            for market_data in market_data_entries:
                # Create price history entry
                price_history = PriceHistory(
                    market_data_id=market_data.id,
                    timestamp=datetime.utcnow(),
                    price=market_data.current_price,
                    volume=market_data.volume_24h,
                    listings_count=market_data.listings_count,
                    data_source="poe_api"
                )
                
                self.db.add(price_history)
            
            self.db.commit()
            logger.info(f"Added price history for {len(market_data_entries)} items")
            
        except Exception as e:
            logger.error(f"Error collecting price history: {e}")
            self.db.rollback()
    
    async def update_specific_modifiers(self, modifier_ids: list):
        """Update market data for specific modifiers."""
        logger.info(f"Updating market data for specific modifiers: {modifier_ids}")
        
        try:
            result = await self.market_service.refresh_market_data(modifier_ids)
            logger.info(f"Updated {result.get('updated_count', 0)} modifiers")
            return result
            
        except Exception as e:
            logger.error(f"Error updating specific modifiers: {e}")
            return {"updated_count": 0, "error": str(e)}


async def main():
    """Main function for data collection."""
    import argparse
    
    parser = argparse.ArgumentParser(description="PoE 2 Tablet Optimizer Data Collection")
    parser.add_argument("--mode", choices=["full", "market", "history", "metadata"], 
                       default="full", help="Collection mode")
    parser.add_argument("--modifiers", nargs="+", type=int, 
                       help="Specific modifier IDs to update")
    
    args = parser.parse_args()
    
    collector = DataCollector()
    
    try:
        if args.mode == "full":
            await collector.collect_all_data()
        elif args.mode == "metadata":
            await collector.collect_api_metadata()
        elif args.mode == "market":
            if args.modifiers:
                await collector.update_specific_modifiers(args.modifiers)
            else:
                await collector.update_market_data()
        elif args.mode == "history":
            await collector.collect_price_history()
            
    except KeyboardInterrupt:
        logger.info("Data collection interrupted by user")
    except Exception as e:
        logger.error(f"Data collection failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
